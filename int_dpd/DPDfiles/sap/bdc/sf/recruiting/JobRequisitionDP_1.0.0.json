{"name": "JobRequisition", "version": "1.0.0", "customTransformer": "", "category": "business-object", "type": "primary", "title": "Tracks job openings, manages key details for efficient hiring, compliance, and workforce planning in an ATS.", "status": "In Testing", "namespace": "sap.bdc.sf.recruiting", "responsible": "sap:ach:LOD-SF-RCM", "partOfPackage": "sap.bdc.sf.recruiting:package:DataProducts:v1.0.0", "shortDescription": "Data product that helps track job openings and also manage key details to streamline hiring, ensure compliance, and optimize workforce planning in an ATS.", "description": "Tracks job openings, managing key details to streamline hiring, ensure compliance, and optimize workforce planning in an ATS.", "lineOfBusiness": "", "e2EBusinessProcess": [], "industry": [], "businessSuitePackage": "", "entityTypes": ["sap.sf.recruiting:entityType:AppStatus:v1", "sap.sf.recruiting:entityType:AssessmentPackage:v1", "sap.sf.recruiting:entityType:BaseStatus:v1", "sap.sf.recruiting:entityType:JobRequisition:v1", "sap.sf.recruiting:entityType:JobRequisitionPosting:v1", "sap.sf.recruiting:entityType:StatusGroup:v1", "sap.sf.recruiting:entityType:StatusSet:v1"], "entities": [{"name": "AppStatus", "namespace": "sap.sf.recruiting", "version": "1.0.0", "nodes": [{"name": "AppStatus", "namespace": "sap.sf.recruiting", "version": "1.0.0", "extensible": true, "root": true, "plugins": []}, {"name": "AppStatusLabel", "namespace": "sap.sf.recruiting", "version": "1.0.0", "extensible": true, "root": false, "plugins": []}], "main": false}, {"name": "AssessmentPackage", "namespace": "sap.sf.recruiting", "version": "1.0.0", "nodes": [{"name": "AssessmentPackage", "namespace": "sap.sf.recruiting", "version": "1.0.0", "extensible": true, "root": true, "plugins": []}, {"name": "LocalizedText", "namespace": "sap.sf.recruiting", "version": "1.0.0", "extensible": true, "root": false, "plugins": []}], "main": false}, {"name": "BaseStatus", "namespace": "sap.sf.recruiting", "version": "1.0.0", "nodes": [{"name": "BaseStatus", "namespace": "sap.sf.recruiting", "version": "1.0.0", "extensible": true, "root": true, "plugins": []}], "main": false}, {"name": "JobRequisition", "namespace": "sap.sf.recruiting", "version": "1.0.0", "nodes": [{"name": "JobRequisition", "namespace": "sap.sf.recruiting", "version": "1.0.0", "extensible": true, "root": true, "plugins": []}], "main": false}, {"name": "JobRequisitionPosting", "namespace": "sap.sf.recruiting", "version": "1.0.0", "nodes": [{"name": "JobRequisitionPosting", "namespace": "sap.sf.recruiting", "version": "1.0.0", "extensible": true, "root": true, "plugins": []}], "main": false}, {"name": "StatusGroup", "namespace": "sap.sf.recruiting", "version": "1.0.0", "nodes": [{"name": "StatusGroup", "namespace": "sap.sf.recruiting", "version": "1.0.0", "extensible": true, "root": true, "plugins": []}, {"name": "StatusGroupLabel", "namespace": "sap.sf.recruiting", "version": "1.0.0", "extensible": true, "root": false, "plugins": []}], "main": false}, {"name": "StatusSet", "namespace": "sap.sf.recruiting", "version": "1.0.0", "nodes": [{"name": "StatusSet", "namespace": "sap.sf.recruiting", "version": "1.0.0", "extensible": true, "root": true, "plugins": []}], "main": false}], "links": [{"url": "https://sap2-dev.collibra.com/asset/01989da8-a8a3-7cda-b38f-8d40e37dcd39", "title": "JobRequisition", "description": "Tracks job openings, managing key details to streamline hiring, ensure compliance, and optimize workforce planning in an ATS."}], "transformers": [], "inputPorts": []}