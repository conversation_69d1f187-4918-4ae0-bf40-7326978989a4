{"name": "JobRequisitionPosting", "dpdType": "sap.fos.datasource", "dpdVersion": "v2", "namespace": "sap.bdc.sf.recruiting", "version": "1.0.0", "sourceObjects": [{"name": "JobRequisitionPosting", "namespace": "sap.sf.recruiting", "version": "1.0.0"}], "connector": {"connectorName": "dsapi"}, "metadata": {"csnIds": ["sap.bdc.sf.recruiting:csnDocument:JobRequisitionPosting:v1.0.0"]}, "transformers": [{"transformerName": "sap.bdc.fos:transformer:jsonlDatasourcePluginsV2:v2.0.0", "parameters": [], "plugins": {"jsonlPlugins": [{"jsonlIngest": {"isEnabled": true, "primaryKeyColumn": "subject", "watermarkColumn": "sequence", "watermarkColumnType": "cds.Int64"}, "jsonlMerge": {"isEnabled": true}, "jsonlDecompose": {"sourceColumnName": "data", "isEnabled": true, "normalize": true}}]}}]}