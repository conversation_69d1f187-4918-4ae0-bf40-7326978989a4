{"name": "JobRequisition", "dpdType": "sap.fos.dataproduct", "dpdVersion": "v2", "title": "Tracks job openings, manages key details for efficient hiring, compliance, and workforce planning in an ATS.", "description": "Tracks job openings, managing key details to streamline hiring, ensure compliance, and optimize workforce planning in an ATS.", "shortDescription": "Data product that helps track job openings and also manage key details to streamline hiring, ensure compliance, and optimize workforce planning in an ATS.", "partOfPackage": "sap.bdc.sf.recruiting:package:DataProducts:v1.0.0", "version": "1.0.0", "type": "primary", "responsible": "sap:ach:LOD-SF-RCM", "category": "business-object", "entityTypes": ["sap.sf.recruiting:entityType:AppStatus:v1", "sap.sf.recruiting:entityType:AssessmentPackage:v1", "sap.sf.recruiting:entityType:BaseStatus:v1", "sap.sf.recruiting:entityType:JobRequisition:v1", "sap.sf.recruiting:entityType:JobRequisitionPosting:v1", "sap.sf.recruiting:entityType:StatusGroup:v1", "sap.sf.recruiting:entityType:StatusSet:v1"], "dependsOn": ["sap.bdc.sf.recruiting:dataSource:AppStatus:v1.0.0", "sap.bdc.sf.recruiting:dataSource:AssessmentPackage:v1.0.0", "sap.bdc.sf.recruiting:dataSource:BaseStatus:v1.0.0", "sap.bdc.sf.recruiting:dataSource:JobRequisition:v1.0.0", "sap.bdc.sf.recruiting:dataSource:JobRequisitionPosting:v1.0.0", "sap.bdc.sf.recruiting:dataSource:StatusGroup:v1.0.0", "sap.bdc.sf.recruiting:dataSource:StatusSet:v1.0.0"], "shares": ["sap.bdc.sf.recruiting:share:JobRequisition:v1.0.0"]}