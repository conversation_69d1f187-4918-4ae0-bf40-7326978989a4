{"name": "JobRequisition", "namespace": "sap.bdc.sf.recruiting", "title": "Tracks job openings, manages key details for efficient hiring, compliance, and workforce planning in an ATS.", "shortDescription": "Data product that helps track job openings and also manage key details to streamline hiring, ensure compliance, and optimize workforce planning in an ATS.", "description": "Tracks job openings, managing key details to streamline hiring, ensure compliance, and optimize workforce planning in an ATS.", "version": "1.0.0", "extensible": {"supported": "automatic", "description": "Extensions to the model are automatically integrated in the API"}, "isRuntimeExtensible": true, "hdlfsSchemas": [{"name": "JobRequisition", "shareTables": [{"name": "AppStatus", "version": "1.0.0", "medallionLayer": "silver"}, {"name": "AppStatusLabel", "version": "1.0.0", "medallionLayer": "silver"}, {"name": "AssessmentPackage", "version": "1.0.0", "medallionLayer": "silver"}, {"name": "LocalizedText", "version": "1.0.0", "medallionLayer": "silver"}, {"name": "BaseStatus", "version": "1.0.0", "medallionLayer": "silver"}, {"name": "JobRequisition", "version": "1.0.0", "medallionLayer": "silver"}, {"name": "JobRequisitionPosting", "version": "1.0.0", "medallionLayer": "silver"}, {"name": "StatusGroup", "version": "1.0.0", "medallionLayer": "silver"}, {"name": "StatusGroupLabel", "version": "1.0.0", "medallionLayer": "silver"}, {"name": "StatusSet", "version": "1.0.0", "medallionLayer": "silver"}]}]}