{"name": "CoreWorkforceData", "dpdType": "sap.fos.transformer", "dpdVersion": "v2", "version": "1.0.0", "unixCronScheduleInUTC": "* * * * * */45 0", "namespace": "sap.sf.analytics", "parameters": {}, "sparkConfig": {"spark.executor.instances": 2, "spark.sql.parquet.datetimeRebaseModeInWrite": "CORRECTED", "spark.sql.constraintPropagation.enabled": "false", "spark.driver.extraJavaOptions": "-Xss64m -XX:+PrintFlagsFinal", "spark.executor.extraJavaOptions": "-Xss64m -XX:+PrintFlagsFinal"}, "computeConfig": {"workloadProfile": "default", "sparkVersion": "3.5.3", "resources": {"driver": {"cpu": "4", "memory": "16g"}, "executor": {"cpu": "4", "memory": "16g"}}}, "envConfig": {}, "steps": [{"stepKey": "sf_silver_to_gold_transformation", "packageName": "ayt_fos_workflows", "packageVersion": "1.0.0", "entryPoint": "ayt_fos_workflows.main.core_workforce_data_transformer=main"}]}