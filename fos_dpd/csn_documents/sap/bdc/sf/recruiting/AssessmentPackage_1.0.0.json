{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/recruiting/requisition/v1/metadata/assessmentPackage", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "assessmentPackage", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.bdc.sf.recruiting", "document": {"version": "1.0.0"}}, "definitions": {"assessmentPackage": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Assessment Package", "@EntityRelationship.entityType": "sap.sf.recruiting:assessmentPackage", "@EntityRelationship.entityIds": [{"name": "assessmentPackage", "propertyTypes": ["sap.sf.recruiting:assessmentPackageId"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Package ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:assessmentPackageId"}, "code": {"type": "cds.String", "@EndUserText.label": "Package Code", "length": 256}, "shortName": {"type": "cds.String", "@EndUserText.label": "Short Name", "length": 256}, "name": {"type": "cds.String", "@EndUserText.label": "Package Name", "length": 1000}, "vendor": {"type": "cds.String", "@EndUserText.label": "Vendor Code", "length": 256}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Assessment Package Created By", "length": 45, "@PersonalData.isPotentiallyPersonal": true}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Assessment Package Created Date"}, "modifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Assessment Package Last Modified By", "length": 45, "@PersonalData.isPotentiallyPersonal": true}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Assessment Package Last Modification Date"}, "texts": {"type": "cds.Composition", "target": "assessmentPackage_texts", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "assessmentPackage_id_virtual"]}, "=", {"ref": ["id"]}]}}}, "assessmentPackage_texts": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized Texts", "@EntityRelationship.entityType": "sap.sf.recruiting:assessmentPackage_texts", "elements": {"assessmentPackage_id_virtual": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_assessmentPackage"}, "type": "cds.Integer64", "key": true}, "_assessmentPackage": {"type": "cds.Association", "target": "assessmentPackage", "cardinality": {"max": 1}, "on": [{"ref": ["_assessmentPackage", "id"]}, "=", {"ref": ["assessmentPackage_id_virtual"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Package Local ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:assessmentPackage_textsId"}, "name": {"type": "cds.String", "@EndUserText.label": "Assessment package Name", "length": 1000}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "length": 100}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "<PERSON><PERSON><PERSON>"}}}}}