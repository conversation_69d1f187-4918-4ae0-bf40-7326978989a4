{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/recruiting/application/v1/metadata/appStatus", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "appStatus", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.bdc.sf.recruiting", "document": {"version": "1.0.0"}}, "definitions": {"appStatus": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Job App Status", "@EntityRelationship.entityType": "sap.sf.recruiting:appStatus", "@EntityRelationship.entityIds": [{"name": "appStatus", "propertyTypes": ["sap.sf.recruiting:appStatusId"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Job App Status ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:appStatusId"}, "statusSetId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.recruiting:statusSet", "referencedPropertyType": "sap.sf.recruiting:statusSetId"}], "type": "cds.Integer64", "@EndUserText.label": "Status Set ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "statusId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.recruiting:baseStatus", "referencedPropertyType": "sap.sf.recruiting:baseStatusId"}], "type": "cds.Integer64", "@EndUserText.label": "Application Status ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "groupId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.recruiting:statusGroup", "referencedPropertyType": "sap.sf.recruiting:statusGroupId"}], "type": "cds.Integer64", "@EndUserText.label": "Status Group ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "commentOnSkip": {"type": "cds.String", "@EndUserText.label": "Comment On Skip", "length": 1}, "externalOnly": {"type": "cds.String", "@EndUserText.label": "External Only", "length": 1}, "internalOnly": {"type": "cds.String", "@EndUserText.label": "Internal Only", "length": 1}, "ordinal": {"type": "cds.Integer64", "@EndUserText.label": "Status Order", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "modifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Last Modified by", "length": 100, "@PersonalData.isPotentiallyPersonal": true}, "enabled": {"type": "cds.String", "@EndUserText.label": "Enabled", "length": 1}, "required": {"type": "cds.String", "@EndUserText.label": "Required", "length": 1}, "hirable": {"type": "cds.String", "@EndUserText.label": "Hirable", "length": 1}, "hired": {"type": "cds.String", "@EndUserText.label": "<PERSON><PERSON>", "length": 1}, "appStatusLabels": {"type": "cds.Composition", "target": "appStatus_appStatusLabels", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "App Status Localized Label", "on": [{"ref": ["appStatusLabels", "appStatus_id_virtual"]}, "=", {"ref": ["id"]}]}}}, "appStatus_appStatusLabels": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "App Status Localized Label", "@EntityRelationship.entityType": "sap.sf.recruiting:appStatus_appStatusLabels", "elements": {"appStatus_id_virtual": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_appStatus"}, "type": "cds.Integer64", "key": true}, "_appStatus": {"type": "cds.Association", "target": "appStatus", "cardinality": {"max": 1}, "on": [{"ref": ["_appStatus", "id"]}, "=", {"ref": ["appStatus_id_virtual"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "App Status Label ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:appStatus_appStatusLabelsId"}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "length": 32}, "internalLabel": {"type": "cds.String", "@EndUserText.label": "Internal Status Label", "length": 100}, "externalLabel": {"type": "cds.String", "@EndUserText.label": "External Label", "length": 100}, "referrerLabel": {"type": "cds.String", "@EndUserText.label": "Referrer Label", "length": 100}, "agencyLabel": {"type": "cds.String", "@EndUserText.label": "Agency Label", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "modifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Last Modified by", "length": 100, "@PersonalData.isPotentiallyPersonal": true}}}}}