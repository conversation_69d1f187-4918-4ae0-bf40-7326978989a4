{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/recruiting/requisition/v1/metadata/jobRequisition", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "jobRequisition", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.bdc.sf.recruiting", "document": {"version": "1.0.0"}}, "definitions": {"jobRequisition": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Job Requisition Details", "@EntityRelationship.entityType": "sap.sf.recruiting:jobRequisition", "@EntityRelationship.entityIds": [{"name": "jobRequisition", "propertyTypes": ["sap.sf.recruiting:jobRequisitionId"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Requisition Number", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:jobRequisitionId"}, "templateId": {"type": "cds.Integer64", "@EndUserText.label": "Job Requisition Template Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "internalStatus": {"type": "cds.Decimal", "@EndUserText.label": "Internal Status", "precision": 34, "scale": 1}, "overallScaleName": {"type": "cds.String", "@EndUserText.label": "Overall Scale Name", "length": 100}, "assessRatingScaleName": {"type": "cds.String", "@EndUserText.label": "Assess Rating Scale Name", "length": 100}, "reverseScale": {"type": "cds.String", "@EndUserText.label": "Reverse Scale", "length": 100}, "statusSetId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.recruiting:statusSet", "referencedPropertyType": "sap.sf.recruiting:statusSetId"}], "type": "cds.Integer64", "@EndUserText.label": "Application Status Set Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "templateType": {"type": "cds.String", "@EndUserText.label": "Template Type", "length": 4000}, "appTemplateId": {"type": "cds.Integer64", "@EndUserText.label": "Application Template Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "positionNumber": {"type": "cds.String", "@EndUserText.label": "EC Position Number", "length": 100}, "deleted": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Deleted"}, "parentJobReqId": {"type": "cds.Integer64", "@EndUserText.label": "Parent Job Req Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "closedDateTime": {"type": "cds.DateTime", "@EndUserText.label": "Date Closed"}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Date Created"}, "formDataId": {"type": "cds.Integer64", "@EndUserText.label": "Form Data Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "jobReqGUId": {"type": "cds.String", "@EndUserText.label": "Job Requisition GU Id", "length": 100}, "modifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 4000, "@PersonalData.isPotentiallyPersonal": true}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "modifiedOnBehalfOf": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Last Modified Proxy User Id", "length": 4000, "@PersonalData.isPotentiallyPersonal": true}, "approvedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Approved By", "length": 4000, "@PersonalData.isPotentiallyPersonal": true}, "approvedAt": {"type": "cds.DateTime", "@EndUserText.label": "Approved Date"}, "statusAudits": {"type": "cds.Composition", "target": "jobRequisition_statusAudits", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Custom Status Audit", "on": [{"ref": ["statusAudits", "jobRequisition_id_virtual"]}, "=", {"ref": ["id"]}]}}}, "jobRequisition_statusAudits": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Job Requisition Custom Status Audit", "@EntityRelationship.entityType": "sap.sf.recruiting:jobRequisition_statusAudits", "elements": {"jobRequisition_id_virtual": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_jobRequisition"}, "type": "cds.Integer64", "key": true}, "_jobRequisition": {"type": "cds.Association", "target": "jobRequisition", "cardinality": {"max": 1}, "on": [{"ref": ["_jobRequisition", "id"]}, "=", {"ref": ["jobRequisition_id_virtual"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Revision Number", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:jobRequisition_statusAuditsId"}, "jobReqId": {"type": "cds.Integer64", "@EndUserText.label": "Job Requisition Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:jobRequisition_statusAuditsJobReqId"}, "type": {"type": "cds.Integer64", "@EndUserText.label": "Revision Type", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "customStatusId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:PickListValue", "referencedPropertyType": "sap.sf.extensibility:PickListValueOptionId"}], "type": "cds.String", "@EndUserText.label": "Job Requisition Custom Status Id", "length": 4000}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created Date"}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 4000, "@PersonalData.isPotentiallyPersonal": true}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "modifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 4000, "@PersonalData.isPotentiallyPersonal": true}}}}}