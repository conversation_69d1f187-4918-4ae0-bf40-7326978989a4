{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/learning/asset/v1/metadata/deliveryMethod", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "DeliveryMethod", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.learning", "document": {"version": "1.0.0"}}, "definitions": {"DeliveryMethod": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Delivery Method", "@EntityRelationship.entityType": "sap.sf.learning:DeliveryMethod", "@EntityRelationship.entityIds": [{"name": "DeliveryMethod", "propertyTypes": ["sap.sf.learning:DeliveryMethodId"]}], "elements": {"id": {"type": "cds.String", "@EndUserText.label": "Delivery Method ID", "length": 90, "key": true, "@EntityRelationship.propertyType": "sap.sf.learning:DeliveryMethodId"}, "description": {"type": "cds.String", "@EndUserText.label": "Delivery Method Description", "length": 4000}, "texts": {"type": "cds.Composition", "target": "deliveryMethod_texts", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Delivery Method Description localized texts", "on": [{"ref": ["texts", "DeliveryMethod_id_virtual"]}, "=", {"ref": ["id"]}]}, "lastUpdatedUser": {"type": "cds.String", "@EndUserText.label": "Last Updated By", "length": 90}, "lastUpdatedTimestamp": {"type": "cds.DateTime", "@EndUserText.label": "Last Updated Timestamp"}}}, "deliveryMethod_texts": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Delivery Method Localized Texts", "@EntityRelationship.entityType": "sap.sf.learning:deliveryMethod_texts", "elements": {"DeliveryMethod_id_virtual": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_DeliveryMethod"}, "type": "cds.String", "length": 90, "key": true}, "_DeliveryMethod": {"type": "cds.Association", "target": "DeliveryMethod", "cardinality": {"max": 1}, "on": [{"ref": ["_DeliveryMethod", "id"]}, "=", {"ref": ["DeliveryMethod_id_virtual"]}]}, "description": {"type": "cds.String", "@EndUserText.label": "Delivery Method Description (Localized)", "length": 4000}, "locale": {"type": "cds.String", "@EndUserText.label": "Filed Locale", "length": 10, "key": true, "@EntityRelationship.propertyType": "sap.sf.learning:deliveryMethod_textsLocale"}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Default Locale"}}}}}