{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/learning/asset/v1/metadata/program", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "Program", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.learning", "document": {"version": "1.0.0"}}, "definitions": {"Program": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Program", "@ODM.entityName": "LearningProgram", "@EntityRelationship.entityType": "sap.sf.learning:Program", "@EntityRelationship.entityIds": [{"name": "Program", "propertyTypes": ["sap.sf.learning:ProgramId"]}], "elements": {"domainId": {"type": "cds.String", "@EndUserText.label": "Program Domain", "length": 90}, "active": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Active"}, "programId": {"type": "cds.String", "@EndUserText.label": "User-Assigned Program ID", "length": 90}, "title": {"type": "cds.String", "@EndUserText.label": "Title", "length": 4000}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 4000}, "texts": {"type": "cds.Composition", "target": "program_texts", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Fields", "on": [{"ref": ["texts", "Program_id_virtual"]}, "=", {"ref": ["id"]}]}, "source": {"type": "cds.String", "@EndUserText.label": "Program Source", "length": 90}, "capabilities": {"type": "cds.Composition", "target": "program_capabilities", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "List of Capabilities", "on": [{"ref": ["capabilities", "Program_id_virtual"]}, "=", {"ref": ["id"]}]}, "productionReady": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Production Ready"}, "thumbnailAttachmentId": {"type": "cds.Integer64", "@EndUserText.label": "<PERSON><PERSON><PERSON><PERSON> ID"}, "capabilitySource": {"type": "cds.String", "@EndUserText.label": "Attribute Source", "length": 90}, "rating": {"type": "cds.Decimal", "@EndUserText.label": "Program Rating", "precision": 34, "scale": 33}, "ratingCount": {"type": "cds.Integer64", "@EndUserText.label": "Total Ratings"}, "programDuration": {"type": "cds.Decimal", "@EndUserText.label": "Program Duration ", "precision": 34, "scale": 17}, "id": {"type": "cds.String", "@EndUserText.label": "Program ID", "length": 90, "key": true, "@EntityRelationship.propertyType": "sap.sf.learning:ProgramId"}, "lastUpdatedUser": {"type": "cds.String", "@EndUserText.label": "Last Updated By", "length": 90}, "lastUpdatedTimestamp": {"type": "cds.DateTime", "@EndUserText.label": "Last Updated Timestamp"}}}, "program_texts": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Learning Item Localized Texts", "@EntityRelationship.entityType": "sap.sf.learning:program_texts", "elements": {"Program_id_virtual": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Program"}, "type": "cds.String", "length": 90, "key": true}, "_Program": {"type": "cds.Association", "target": "Program", "cardinality": {"max": 1}, "on": [{"ref": ["_Program", "id"]}, "=", {"ref": ["Program_id_virtual"]}]}, "title": {"type": "cds.String", "@EndUserText.label": "Title"}, "description": {"type": "cds.String", "@EndUserText.label": "Description"}, "locale": {"type": "cds.String", "@EndUserText.label": "Filed Locale", "length": 10, "key": true, "@EntityRelationship.propertyType": "sap.sf.learning:program_textsLocale"}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Default Locale"}}}, "program_capabilities": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Learning Capabilities", "@EntityRelationship.entityType": "sap.sf.learning:program_capabilities", "elements": {"Program_id_virtual": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Program"}, "type": "cds.String", "length": 90, "key": true}, "_Program": {"type": "cds.Association", "target": "Program", "cardinality": {"max": 1}, "on": [{"ref": ["_Program", "id"]}, "=", {"ref": ["Program_id_virtual"]}]}, "internalId": {"type": "cds.String", "@EndUserText.label": "Internal ID", "length": 90, "key": true, "@EntityRelationship.propertyType": "sap.sf.learning:program_capabilitiesInternalId"}, "externalId": {"type": "cds.String", "@EndUserText.label": "External ID", "length": 90}, "rating": {"type": "cds.Integer64", "@EndUserText.label": "Rating"}, "tihExternalId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.capabilities:Library", "referencedPropertyType": "sap.sf.capabilities:LibraryId"}], "type": "cds.String", "@EndUserText.label": "TIH External ID", "length": 90}, "jpbExternalId": {"type": "cds.String", "@EndUserText.label": "JPB External ID", "length": 90}, "jdmExternalId": {"type": "cds.String", "@EndUserText.label": "JDB External ID", "length": 90}}}}}