{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/learning/asset/v1/metadata/courseType", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "CourseType", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.learning", "document": {"version": "1.0.0"}}, "definitions": {"CourseType": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Course Type", "@EntityRelationship.entityType": "sap.sf.learning:CourseType", "@EntityRelationship.entityIds": [{"name": "CourseType", "propertyTypes": ["sap.sf.learning:CourseTypeId"]}], "elements": {"id": {"type": "cds.String", "@EndUserText.label": "Course Type ID", "length": 90, "key": true, "@EntityRelationship.propertyType": "sap.sf.learning:CourseTypeId"}, "description": {"type": "cds.String", "@EndUserText.label": "Course Type Description", "length": 4000}, "systemProvided": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "System-Generated Course Type ID"}, "visible": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Course Type Visible"}, "texts": {"type": "cds.Composition", "target": "courseType_texts", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Fields", "on": [{"ref": ["texts", "CourseType_id_virtual"]}, "=", {"ref": ["id"]}]}, "lastUpdatedUser": {"type": "cds.String", "@EndUserText.label": "Last Updated By", "length": 90}, "lastUpdatedTimestamp": {"type": "cds.DateTime", "@EndUserText.label": "Last Updated Timestamp"}}}, "courseType_texts": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Course Type Localized Texts", "@EntityRelationship.entityType": "sap.sf.learning:courseType_texts", "elements": {"CourseType_id_virtual": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_CourseType"}, "type": "cds.String", "length": 90, "key": true}, "_CourseType": {"type": "cds.Association", "target": "CourseType", "cardinality": {"max": 1}, "on": [{"ref": ["_CourseType", "id"]}, "=", {"ref": ["CourseType_id_virtual"]}]}, "description": {"type": "cds.String", "@EndUserText.label": "Course Type Description  (Localized)", "length": 4000}, "locale": {"type": "cds.String", "@EndUserText.label": "Filed Locale", "length": 10, "key": true, "@EntityRelationship.propertyType": "sap.sf.learning:courseType_textsLocale"}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Default Locale"}}}}}