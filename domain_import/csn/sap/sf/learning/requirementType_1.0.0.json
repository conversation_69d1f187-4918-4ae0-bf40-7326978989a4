{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/learning/asset/v1/metadata/requirementType", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "RequirementType", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.learning", "document": {"version": "1.0.0"}}, "definitions": {"RequirementType": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Requirement Type", "@EntityRelationship.entityType": "sap.sf.learning:RequirementType", "@EntityRelationship.entityIds": [{"name": "RequirementType", "propertyTypes": ["sap.sf.learning:RequirementTypeId"]}], "elements": {"id": {"type": "cds.String", "@EndUserText.label": "Requirement Type ID", "length": 90, "key": true, "@EntityRelationship.propertyType": "sap.sf.learning:RequirementTypeId"}, "required": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Required"}, "priority": {"type": "cds.Integer64", "@EndUserText.label": "Requirement Type Priority"}, "description": {"type": "cds.String", "@EndUserText.label": "Requirement Type Description", "length": 4000}, "texts": {"type": "cds.Composition", "target": "requirementType_texts", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Fields", "on": [{"ref": ["texts", "RequirementType_id_virtual"]}, "=", {"ref": ["id"]}]}, "lastUpdatedUser": {"type": "cds.String", "@EndUserText.label": "Last Updated By", "length": 90}, "lastUpdatedTimestamp": {"type": "cds.DateTime", "@EndUserText.label": "Last Updated Timestamp"}}}, "requirementType_texts": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Requirement Type Localized Texts", "@EntityRelationship.entityType": "sap.sf.learning:requirementType_texts", "elements": {"RequirementType_id_virtual": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_RequirementType"}, "type": "cds.String", "length": 90, "key": true}, "_RequirementType": {"type": "cds.Association", "target": "RequirementType", "cardinality": {"max": 1}, "on": [{"ref": ["_RequirementType", "id"]}, "=", {"ref": ["RequirementType_id_virtual"]}]}, "description": {"type": "cds.String", "@EndUserText.label": "Requirement Type Description (Localized)", "length": 4000}, "locale": {"type": "cds.String", "@EndUserText.label": "Filed Locale", "length": 10, "key": true, "@EntityRelationship.propertyType": "sap.sf.learning:requirementType_textsLocale"}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Default Locale"}}}}}