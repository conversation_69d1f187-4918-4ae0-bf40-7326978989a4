{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/learning/asset/v1/metadata/completionStatus", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "CompletionStatus", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.learning", "document": {"version": "1.0.0"}}, "definitions": {"CompletionStatus": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Completion Status", "@EntityRelationship.entityType": "sap.sf.learning:CompletionStatus", "@EntityRelationship.entityIds": [{"name": "CompletionStatus", "propertyTypes": ["sap.sf.learning:CompletionStatusId"]}], "elements": {"id": {"type": "cds.String", "@EndUserText.label": "Completion Status ID", "length": 90, "key": true, "@EntityRelationship.propertyType": "sap.sf.learning:CompletionStatusId"}, "description": {"type": "cds.String", "@EndUserText.label": "Completion Status Description", "length": 4000}, "courseTypeId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.learning:CourseType", "referencedPropertyType": "sap.sf.learning:CourseTypeId"}], "type": "cds.String", "@EndUserText.label": "Course Type for Completion Status", "length": 90}, "provideCredit": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Credit Given"}, "texts": {"type": "cds.Composition", "target": "completionStatus_texts", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Fields", "on": [{"ref": ["texts", "CompletionStatus_id_virtual"]}, "=", {"ref": ["id"]}]}, "lastUpdatedUser": {"type": "cds.String", "@EndUserText.label": "Last Updated By", "length": 90}, "lastUpdatedTimestamp": {"type": "cds.DateTime", "@EndUserText.label": "Last Updated Timestamp"}}}, "completionStatus_texts": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Completion Status Localized Texts", "@EntityRelationship.entityType": "sap.sf.learning:completionStatus_texts", "elements": {"CompletionStatus_id_virtual": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_CompletionStatus"}, "type": "cds.String", "length": 90, "key": true}, "_CompletionStatus": {"type": "cds.Association", "target": "CompletionStatus", "cardinality": {"max": 1}, "on": [{"ref": ["_CompletionStatus", "id"]}, "=", {"ref": ["CompletionStatus_id_virtual"]}]}, "description": {"type": "cds.String", "@EndUserText.label": "Completion Status Description (Localized)", "length": 4000}, "locale": {"type": "cds.String", "@EndUserText.label": "Filed Locale", "length": 10, "key": true, "@EntityRelationship.propertyType": "sap.sf.learning:completionStatus_textsLocale"}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Default Locale"}}}}}