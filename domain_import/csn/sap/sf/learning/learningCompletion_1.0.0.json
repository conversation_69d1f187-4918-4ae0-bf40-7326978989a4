{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/learning/history/v1/metadata/learningCompletion", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "LearningCompletion", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.learning", "document": {"version": "1.0.0"}}, "definitions": {"LearningCompletion": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Learning History", "@ODM.entityName": "LearningHistory", "@EntityRelationship.entityType": "sap.sf.learning:LearningCompletion", "@EntityRelationship.entityIds": [{"name": "LearningCompletion", "propertyTypes": ["sap.sf.learning:LearningCompletionId"]}], "elements": {"userID": {"type": "cds.String", "@EndUserText.label": "User ID", "length": 90}, "componentID": {"type": "cds.String", "@EndUserText.label": "Component ID", "length": 90}, "componentTypeID": {"type": "cds.String", "@EndUserText.label": "Course Type ID", "length": 90}, "revisionDate": {"type": "cds.DateTime", "@EndUserText.label": "Revision Date"}, "completionStatusID": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.learning:CompletionStatus", "referencedPropertyType": "sap.sf.learning:CompletionStatusId"}], "type": "cds.String", "@EndUserText.label": "Completion Status", "length": 90}, "hasCredit": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Credit Given"}, "completionDate": {"type": "cds.DateTime", "@EndUserText.label": "Completion Date"}, "learningCompletionEventSysGUID": {"type": "cds.String", "@EndUserText.label": "Learning Completion ID", "length": 90, "key": true, "@EntityRelationship.propertyType": "sap.sf.learning:LearningCompletionId"}, "learningItemType": {"type": "cds.String", "@EndUserText.label": "Learning Entity Type", "length": 90}, "learningItemID": {"type": "cds.String", "@EndUserText.label": "Learning Entity ID", "length": 90}, "courseId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.learning:Course", "referencedPropertyType": "sap.sf.learning:CourseId"}], "type": "cds.String", "@EndUserText.label": "Course ID", "length": 90}, "programId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.learning:Program", "referencedPropertyType": "sap.sf.learning:ProgramId"}], "type": "cds.String", "@EndUserText.label": "Program ID", "length": 90}, "lastUpdatedTimestamp": {"type": "cds.DateTime", "@EndUserText.label": "Last Updated Timestamp"}, "componentKey": {"type": "cds.Integer64", "@EndUserText.label": "Component Key"}, "totalHours": {"type": "cds.Decimal", "@EndUserText.label": "Total Hours", "precision": 34, "scale": 17}, "creditHours": {"type": "cds.Decimal", "@EndUserText.label": "Credit Hours", "precision": 34, "scale": 17}, "cpeHours": {"type": "cds.Decimal", "@EndUserText.label": "CPE Hours", "precision": 34, "scale": 17}, "contactHours": {"type": "cds.Decimal", "@EndUserText.label": "Contact Hours", "precision": 34, "scale": 17}, "personId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkforcePersonProfile", "referencedPropertyType": "sap.sf.workforce:WorkforcePersonProfileId"}], "type": "cds.String", "@EndUserText.label": "Person ID", "length": 90}}}}}