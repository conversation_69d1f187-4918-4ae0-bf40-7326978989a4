{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/learning/asset/v1/metadata/course", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "Course", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.learning", "document": {"version": "1.0.0"}}, "definitions": {"Course": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Course", "@ODM.entityName": "LearningCourse", "@EntityRelationship.entityType": "sap.sf.learning:Course", "@EntityRelationship.entityIds": [{"name": "Course", "propertyTypes": ["sap.sf.learning:CourseId"]}], "elements": {"courseDuration": {"type": "cds.Decimal", "@EndUserText.label": "Course Duration", "precision": 34, "scale": 17}, "capabilities": {"type": "cds.Composition", "target": "course_capabilities", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Attributes", "on": [{"ref": ["capabilities", "Course_learningItemID_virtual"]}, "=", {"ref": ["learningItemID"]}]}, "requirementTypeID": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.learning:RequirementType", "referencedPropertyType": "sap.sf.learning:RequirementTypeId"}], "type": "cds.String", "@EndUserText.label": "Requirement Type ID", "length": 90}, "enableRating": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Rateable"}, "includePreviousRevisionRating": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Previous Revisions Included"}, "cpeHours": {"type": "cds.Decimal", "@EndUserText.label": "CPE Hours", "precision": 34, "scale": 17}, "contactHours": {"type": "cds.Decimal", "@EndUserText.label": "Contact Hours", "precision": 34, "scale": 17}, "sourceId": {"type": "cds.String", "@EndUserText.label": "Course Source ID", "length": 90}, "thumbnail": {"type": "cds.String", "@EndUserText.label": "Course Thumbnail", "length": 1000}, "rating": {"type": "cds.Decimal", "@EndUserText.label": "Course Rating", "precision": 34, "scale": 33}, "ratingCount": {"type": "cds.Integer64", "@EndUserText.label": "Total Ratings"}, "mobileAccessible": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Accessible on Mobile"}, "thumbnailAttachmentId": {"type": "cds.Integer64", "@EndUserText.label": "Course Thumbnail id"}, "deliveryMethod": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.learning:DeliveryMethod", "referencedPropertyType": "sap.sf.learning:DeliveryMethodId"}], "type": "cds.String", "@EndUserText.label": "Course Delivery Method", "length": 90}, "createdTimestamp": {"type": "cds.DateTime", "@EndUserText.label": "Created Timestamp"}, "revisionNumber": {"type": "cds.String", "@EndUserText.label": "Revision Number", "length": 90}, "reviserName": {"type": "cds.String", "@EndUserText.label": "Revised By", "length": 90}, "approverName": {"type": "cds.String", "@EndUserText.label": "Approver Name", "length": 90}, "approvalTimestamp": {"type": "cds.DateTime", "@EndUserText.label": "Approval Timestamp"}, "gradingMethod": {"type": "cds.Integer64", "@EndUserText.label": "Course Grading Method"}, "approvalRequired": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Approval Required"}, "capabilitySource": {"type": "cds.String", "@EndUserText.label": "Capability Source", "length": 90}, "courseTitle": {"type": "cds.String", "@EndUserText.label": "Course Title", "length": 4000}, "courseDescription": {"type": "cds.String", "@EndUserText.label": "Course Description", "length": 4000}, "texts": {"type": "cds.Composition", "target": "course_texts", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Fields", "on": [{"ref": ["texts", "Course_learningItemID_virtual"]}, "=", {"ref": ["learningItemID"]}]}, "creditHours": {"type": "cds.Decimal", "@EndUserText.label": "Credit Hours", "precision": 34, "scale": 17}, "componentID": {"type": "cds.String", "@EndUserText.label": "Course ID", "length": 90}, "componentTypeID": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.learning:CourseType", "referencedPropertyType": "sap.sf.learning:CourseTypeId"}], "type": "cds.String", "@EndUserText.label": "Course Type ID", "length": 90}, "revisionDate": {"type": "cds.DateTime", "@EndUserText.label": "Revision Date"}, "componentKey": {"type": "cds.Integer64", "@EndUserText.label": "Component Key"}, "classificationID": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.learning:Classification", "referencedPropertyType": "sap.sf.learning:ClassificationId"}], "type": "cds.String", "@EndUserText.label": "Item Classification ID", "length": 90}, "learningItemID": {"type": "cds.String", "@EndUserText.label": "Learning Item ID", "length": 90, "key": true, "@EntityRelationship.propertyType": "sap.sf.learning:CourseId"}, "learningItemType": {"type": "cds.String", "@EndUserText.label": "Course Type", "length": 90}, "domainID": {"type": "cds.String", "@EndUserText.label": "Course Domain", "length": 90}, "active": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Course Status"}, "activeOldValue": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Active Old Value"}, "active@changed": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Active Changed"}, "productionReady": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Production Ready"}, "showInCatalog": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Displayed in Catalog"}, "lastUpdatedUser": {"type": "cds.String", "@EndUserText.label": "Last Updated By", "length": 90}, "lastUpdatedTimestamp": {"type": "cds.DateTime", "@EndUserText.label": "Last Updated Timestamp"}}}, "course_capabilities": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Learning Capabilities", "@EntityRelationship.entityType": "sap.sf.learning:course_capabilities", "elements": {"Course_learningItemID_virtual": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Course"}, "type": "cds.String", "length": 90, "key": true}, "_Course": {"type": "cds.Association", "target": "Course", "cardinality": {"max": 1}, "on": [{"ref": ["_Course", "learningItemID"]}, "=", {"ref": ["Course_learningItemID_virtual"]}]}, "internalId": {"type": "cds.String", "@EndUserText.label": "Internal ID", "length": 90, "key": true, "@EntityRelationship.propertyType": "sap.sf.learning:course_capabilitiesInternalId"}, "externalId": {"type": "cds.String", "@EndUserText.label": "External ID", "length": 90}, "rating": {"type": "cds.Integer64", "@EndUserText.label": "Rating"}, "tihExternalId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.capabilities:Library", "referencedPropertyType": "sap.sf.capabilities:LibraryId"}], "type": "cds.String", "@EndUserText.label": "TIH External ID", "length": 90}, "jpbExternalId": {"type": "cds.String", "@EndUserText.label": "JPB External ID", "length": 90}, "jdmExternalId": {"type": "cds.String", "@EndUserText.label": "JDB External ID", "length": 90}}}, "course_texts": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Course Localized Texts", "@EntityRelationship.entityType": "sap.sf.learning:course_texts", "elements": {"Course_learningItemID_virtual": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Course"}, "type": "cds.String", "length": 90, "key": true}, "_Course": {"type": "cds.Association", "target": "Course", "cardinality": {"max": 1}, "on": [{"ref": ["_Course", "learningItemID"]}, "=", {"ref": ["Course_learningItemID_virtual"]}]}, "courseTitle": {"type": "cds.String", "@EndUserText.label": "Course Title"}, "courseDescription": {"type": "cds.String", "@EndUserText.label": "Course Description"}, "locale": {"type": "cds.String", "@EndUserText.label": "Filed Locale", "length": 10, "key": true, "@EntityRelationship.propertyType": "sap.sf.learning:course_textsLocale"}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Default Locale"}}}}}