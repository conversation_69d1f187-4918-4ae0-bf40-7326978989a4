{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/learning/asset/v1/metadata/classification", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "Classification", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.learning", "document": {"version": "1.0.0"}}, "definitions": {"Classification": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Item Classification", "@EntityRelationship.entityType": "sap.sf.learning:Classification", "@EntityRelationship.entityIds": [{"name": "Classification", "propertyTypes": ["sap.sf.learning:ClassificationId"]}], "elements": {"id": {"type": "cds.String", "@EndUserText.label": "Item Classification ID", "length": 90, "key": true, "@EntityRelationship.propertyType": "sap.sf.learning:ClassificationId"}, "description": {"type": "cds.String", "@EndUserText.label": "Item Classification Description", "length": 4000}, "texts": {"type": "cds.Composition", "target": "classification_texts", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Fields", "on": [{"ref": ["texts", "Classification_id_virtual"]}, "=", {"ref": ["id"]}]}, "lastUpdatedUser": {"type": "cds.String", "@EndUserText.label": "Last Updated By", "length": 90}, "lastUpdatedTimestamp": {"type": "cds.DateTime", "@EndUserText.label": "Last Updated Timestamp"}}}, "classification_texts": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Classification Localized Texts", "@EntityRelationship.entityType": "sap.sf.learning:classification_texts", "elements": {"Classification_id_virtual": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Classification"}, "type": "cds.String", "length": 90, "key": true}, "_Classification": {"type": "cds.Association", "target": "Classification", "cardinality": {"max": 1}, "on": [{"ref": ["_Classification", "id"]}, "=", {"ref": ["Classification_id_virtual"]}]}, "description": {"type": "cds.String", "@EndUserText.label": "Classification Description", "length": 4000}, "locale": {"type": "cds.String", "@EndUserText.label": "Filed Locale", "length": 10, "key": true, "@EntityRelationship.propertyType": "sap.sf.learning:classification_textsLocale"}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Default Locale"}}}}}