{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/enterprisestructure/v1/metadata/legalEntity", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "LegalEntity", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"LegalEntity": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Legal Entity", "@ODM.entityName": "CompanyCode", "@ODM.oid": "entityOID", "@EntityRelationship.entityType": "sap.sf.foundationobjects:LegalEntity", "@EntityRelationship.temporalIds": [{"name": "LegalEntity", "propertyTypes": ["sap.sf.foundationobjects:LegalEntityId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:LegalEntityId"}, "entityOID": {"type": "cds.String", "@EndUserText.label": "entityOID", "length": 128}, "externalCode": {"type": "cds.String", "@EndUserText.label": "Code", "length": 32}, "timeslices": {"type": "cds.Composition", "target": "LegalEntityTimeSlice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Timeslices", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "LegalEntityTimeSlice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "LegalEntityTimeSlice", "@EntityRelationship.entityType": "sap.sf.foundationobjects:LegalEntityTimeSlice", "@EntityRelationship.temporalIds": [{"name": "LegalEntityTimeSlice", "propertyTypes": ["sap.sf.foundationobjects:LegalEntityTimeSliceRecordId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_LegalEntity"}, "type": "cds.Integer64", "key": true}, "_LegalEntity": {"type": "cds.Association", "target": "LegalEntity", "cardinality": {"max": 1}, "on": [{"ref": ["_LegalEntity", "id"]}, "=", {"ref": ["_id"]}]}, "validFrom": {"type": "cds.Date", "@EndUserText.label": "Effective as of"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 90}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128}, "effectiveStatus": {"type": "cds.String", "@EndUserText.label": "Status", "length": 128}, "defaultPayGroup": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:payGroup", "referencedPropertyType": "sap.sf.foundationobjects:payGroupId"}], "@EndUserText.label": "Id"}, "defaultPayGroupId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:PayGroup", "referencedPropertyType": "sap.sf.foundationobjects:PayGroupId"}], "type": "cds.Integer64", "@EndUserText.label": "Default Pay Group"}, "defaultLocation": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:location", "referencedPropertyType": "sap.sf.foundationobjects:locationId"}], "@EndUserText.label": "Id"}, "defaultLocationId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Location", "referencedPropertyType": "sap.sf.foundationobjects:LocationId"}], "type": "cds.Integer64", "@EndUserText.label": "Default Location"}, "standardWeeklyHours": {"type": "cds.Decimal", "@EndUserText.label": "Standard Weekly Hours", "precision": 34, "scale": 17}, "currency": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:currency", "referencedPropertyType": "sap.sf.foundationobjects:currencyId"}], "@EndUserText.label": "Id"}, "currencyId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Currency", "referencedPropertyType": "sap.sf.foundationobjects:CurrencyId"}], "type": "cds.Integer64", "@EndUserText.label": "<PERSON><PERSON><PERSON><PERSON>"}, "officialLanguage": {"type": "cds.String", "@EndUserText.label": "Official Language", "length": 255}, "countryOfRegistration": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:country", "referencedPropertyType": "sap.sf.foundationobjects:countryId"}], "@EndUserText.label": "Id"}, "countryOfRegistrationId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Country", "referencedPropertyType": "sap.sf.foundationobjects:CountryId"}], "type": "cds.Integer64", "@EndUserText.label": "countryOfRegistration"}, "autoCreated": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "autoCreated"}, "validTo": {"type": "cds.Date", "@EndUserText.label": "Effective End Date"}, "createdAssignment": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "@EndUserText.label": "Id"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:LegalEntityTimeSliceRecordId"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "toNameFormat": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:nameFormat", "referencedPropertyType": "sap.sf.foundationobjects:nameFormatId"}], "@EndUserText.label": "Id"}, "toNameFormatId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:NameFormatGO", "referencedPropertyType": "sap.sf.foundationobjects:NameFormatGOId"}], "type": "cds.Integer64", "@EndUserText.label": "Name Format for Legal Entity"}, "toDisplayNameFormatId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:NameFormatGO", "referencedPropertyType": "sap.sf.foundationobjects:NameFormatGOId"}], "type": "cds.Integer64", "@EndUserText.label": "Name Format for General Display"}, "texts": {"type": "cds.Composition", "target": "LegalEntityText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_recordId"]}, "=", {"ref": ["recordId"]}]}}}, "LegalEntityText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized value for translatable data type field", "@EntityRelationship.entityType": "sap.sf.foundationobjects:LegalEntityText", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_LegalEntityTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_LegalEntityTimeSlice": {"type": "cds.Association", "target": "LegalEntityTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_LegalEntityTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "length": 128, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:LegalEntityTextLocale"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 90}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "default"}}}}}