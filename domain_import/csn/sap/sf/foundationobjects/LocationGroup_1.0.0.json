{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/enterprisestructure/v1/metadata/locationGroup", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "LocationGroup", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"LocationGroup": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Location Group", "@ODM.entityName": "WorkforceLocationGroup", "@EntityRelationship.entityType": "sap.sf.foundationobjects:LocationGroup", "@EntityRelationship.temporalIds": [{"name": "LocationGroup", "propertyTypes": ["sap.sf.foundationobjects:LocationGroupId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Internal Code", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:LocationGroupId"}, "externalCode": {"type": "cds.String", "@EndUserText.label": "Code", "length": 32}, "timeslices": {"type": "cds.Composition", "target": "LocationGroupTimeslice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Location Group", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "LocationGroupTimeslice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Location Group", "@EntityRelationship.entityType": "sap.sf.foundationobjects:LocationGroupTimeslice", "@EntityRelationship.temporalIds": [{"name": "LocationGroupTimeslice", "propertyTypes": ["sap.sf.foundationobjects:LocationGroupTimesliceId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_LocationGroup"}, "type": "cds.Integer64", "key": true}, "_LocationGroup": {"type": "cds.Association", "target": "LocationGroup", "cardinality": {"max": 1}, "on": [{"ref": ["_LocationGroup", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Object ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:LocationGroupTimesliceId", "@ObjectModel.text.association": {"=": "texts"}}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 32}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128}, "status": {"type": "cds.String", "@EndUserText.label": "Status"}, "startDate": {"type": "cds.Date", "@EndUserText.label": "Start Date"}, "endDate": {"type": "cds.Date", "@EndUserText.label": "End Date"}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}, "texts": {"type": "cds.Composition", "target": "LocationGroupText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "texts", "on": [{"ref": ["texts", "_id"]}, "=", {"ref": ["id"]}]}}}, "LocationGroupText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "LocationGroupText", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.foundationobjects:LocationGroupText", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_LocationGroupTimeslice"}, "type": "cds.Integer64", "key": true}, "_LocationGroupTimeslice": {"type": "cds.Association", "target": "LocationGroupTimeslice", "cardinality": {"max": 1}, "on": [{"ref": ["_LocationGroupTimeslice", "id"]}, "=", {"ref": ["_id"]}]}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 32, "@Semantics.text": true}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128, "@Semantics.text": true}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:LocationGroupTextLocale", "@Semantics.language": true}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "<PERSON><PERSON><PERSON>"}}}}}