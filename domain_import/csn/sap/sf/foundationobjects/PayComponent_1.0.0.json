{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/compensation/v1/metadata/payComponent", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "PayComponent", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"PayComponent": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Pay Component", "@ODM.entityName": "PayComponent", "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayComponent", "@EntityRelationship.temporalIds": [{"name": "PayComponent", "propertyTypes": ["sap.sf.foundationobjects:PayComponentId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Internal Code", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayComponentId"}, "externalCode": {"type": "cds.String", "@EndUserText.label": "Pay Component ID", "length": 32}, "timeslices": {"type": "cds.Composition", "target": "PayComponentTimeslice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Pay Component", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "PayComponentTimeslice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Pay Component", "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayComponentTimeslice", "@EntityRelationship.temporalIds": [{"name": "PayComponentTimeslice", "propertyTypes": ["sap.sf.foundationobjects:PayComponentTimesliceId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PayComponent"}, "type": "cds.Integer64", "key": true}, "_PayComponent": {"type": "cds.Association", "target": "PayComponent", "cardinality": {"max": 1}, "on": [{"ref": ["_PayComponent", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Object ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayComponentTimesliceId", "@ObjectModel.text.association": {"=": "texts"}}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 32}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128}, "status": {"type": "cds.String", "@EndUserText.label": "Status"}, "startDate": {"type": "cds.Date", "@EndUserText.label": "Start Date"}, "endDate": {"type": "cds.Date", "@EndUserText.label": "End Date"}, "payComponentType": {"type": "cds.String", "@EndUserText.label": "Pay Component Type", "length": 32}, "isEarning": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Earning"}, "currency": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Currency", "referencedPropertyType": "sap.sf.foundationobjects:CurrencyId"}], "type": "cds.String", "@EndUserText.label": "<PERSON><PERSON><PERSON><PERSON>"}, "payComponentValue": {"type": "cds.Decimal", "@EndUserText.label": "Pay Component Value", "precision": 34, "scale": 3}, "frequencyCode": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Frequency", "referencedPropertyType": "sap.sf.foundationobjects:FrequencyId"}], "type": "cds.Integer64", "@EndUserText.label": "Frequency", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "recurring": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Recurring"}, "basePayComponentGroup": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:PayComponentGroup", "referencedPropertyType": "sap.sf.foundationobjects:PayComponentGroupId"}], "type": "cds.Integer64", "@EndUserText.label": "Base Pay Component Group", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "taxTreatment": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:TAXTREATMENTPickList", "referencedPropertyType": "sap.sf.extensibility:TAXTREATMENTPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Tax Treatment", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "canOverride": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Can Override"}, "selfServiceDescription": {"type": "cds.String", "@EndUserText.label": "Self Service Description", "length": 32}, "displayOnSelfService": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Display on Self Service"}, "usedForCompPlanning": {"type": "cds.String", "@EndUserText.label": "Used for Comp Planning", "length": 32}, "target": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Target"}, "maxFractionDigits": {"type": "cds.Integer64", "@EndUserText.label": "Maximum Decimal Places", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "unitOfMeasure": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:UnitOfMeasure", "referencedPropertyType": "sap.sf.foundationobjects:UnitOfMeasureId"}], "type": "cds.Integer64", "@EndUserText.label": "Unit of Measure", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "rate": {"type": "cds.Decimal", "@EndUserText.label": "Rate", "precision": 34, "scale": 3}, "number": {"type": "cds.Decimal", "@EndUserText.label": "Number", "precision": 34, "scale": 3}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "isEndDatedPayment": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Enable End-Dated Payments"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}, "texts": {"type": "cds.Composition", "target": "PayComponentText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "texts", "on": [{"ref": ["texts", "_id"]}, "=", {"ref": ["id"]}]}}}, "PayComponentText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "PayComponentText", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayComponentText", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PayComponentTimeslice"}, "type": "cds.Integer64", "key": true}, "_PayComponentTimeslice": {"type": "cds.Association", "target": "PayComponentTimeslice", "cardinality": {"max": 1}, "on": [{"ref": ["_PayComponentTimeslice", "id"]}, "=", {"ref": ["_id"]}]}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 32, "@Semantics.text": true}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128, "@Semantics.text": true}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayComponentTextLocale", "@Semantics.language": true}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "<PERSON><PERSON><PERSON>"}}}}}