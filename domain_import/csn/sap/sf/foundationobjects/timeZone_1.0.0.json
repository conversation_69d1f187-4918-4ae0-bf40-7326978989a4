{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/v1/metadata/timeZone", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "TimeZone", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"TimeZone": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Time Zone", "@ODM.entityName": "TimeZoneCode", "@EntityRelationship.entityType": "sap.sf.foundationobjects:TimeZone", "@EntityRelationship.temporalIds": [{"name": "TimeZone", "propertyTypes": ["sap.sf.foundationobjects:TimeZoneId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Internal Code", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:TimeZoneId"}, "externalCode": {"type": "cds.String", "@EndUserText.label": "Code", "length": 128}, "timeslices": {"type": "cds.Composition", "target": "TimeZoneTimeSlice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Timeslices", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "TimeZoneTimeSlice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "TimeZoneTimeSlice", "@EntityRelationship.entityType": "sap.sf.foundationobjects:TimeZoneTimeSlice", "@EntityRelationship.temporalIds": [{"name": "TimeZoneTimeSlice", "propertyTypes": ["sap.sf.foundationobjects:TimeZoneTimeSliceRecordId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_TimeZone"}, "type": "cds.Integer64", "key": true}, "_TimeZone": {"type": "cds.Association", "target": "TimeZone", "cardinality": {"max": 1}, "on": [{"ref": ["_TimeZone", "id"]}, "=", {"ref": ["_id"]}]}, "validFrom": {"type": "cds.Date", "@EndUserText.label": "Effective as of"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 255}, "effectiveStatus": {"type": "cds.String", "@EndUserText.label": "Status", "length": 128}, "country": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:country", "referencedPropertyType": "sap.sf.foundationobjects:countryId"}], "@EndUserText.label": "Id"}, "countryId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Country", "referencedPropertyType": "sap.sf.foundationobjects:CountryId"}], "type": "cds.Integer64", "@EndUserText.label": "Country/Region"}, "supported": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Time Zone is supported"}, "utcOffset": {"type": "cds.String", "@EndUserText.label": "Offset to UTC", "length": 255}, "utcDstOffset": {"type": "cds.String", "@EndUserText.label": "DST offset to UTC", "length": 255}, "comment": {"type": "cds.String", "@EndUserText.label": "Comment", "length": 255}, "validTo": {"type": "cds.Date", "@EndUserText.label": "Effective End Date"}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:TimeZoneTimeSliceRecordId"}, "createdAssignment": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "@EndUserText.label": "Id"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "texts": {"type": "cds.Composition", "target": "TimeZoneText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_recordId"]}, "=", {"ref": ["recordId"]}]}}}, "TimeZoneText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized value for translatable data type field", "@EntityRelationship.entityType": "sap.sf.foundationobjects:TimeZoneText", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_TimeZoneTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_TimeZoneTimeSlice": {"type": "cds.Association", "target": "TimeZoneTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_TimeZoneTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "length": 128, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:TimeZoneTextLocale"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 255}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "default"}}}}}