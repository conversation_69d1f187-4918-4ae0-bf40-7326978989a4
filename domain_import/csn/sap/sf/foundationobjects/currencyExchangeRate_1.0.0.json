{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/v1/metadata/currencyExchangeRate", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "CurrencyExchangeRate", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"CurrencyExchangeRate": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Currency Exchange Rate", "@ODM.entityName": "ExchangeRate", "@EntityRelationship.entityType": "sap.sf.foundationobjects:CurrencyExchangeRate", "@EntityRelationship.temporalIds": [{"name": "CurrencyExchangeRate", "propertyTypes": ["sap.sf.foundationobjects:CurrencyExchangeRateId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:CurrencyExchangeRateId"}, "externalCode": {"type": "cds.String", "@EndUserText.label": "externalCode", "length": 128}, "timeslices": {"type": "cds.Composition", "target": "CurrencyExchangeRateTimeSlice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Timeslices", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "CurrencyExchangeRateTimeSlice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "CurrencyExchangeRateTimeSlice", "@EntityRelationship.entityType": "sap.sf.foundationobjects:CurrencyExchangeRateTimeSlice", "@EntityRelationship.temporalIds": [{"name": "CurrencyExchangeRateTimeSlice", "propertyTypes": ["sap.sf.foundationobjects:CurrencyExchangeRateTimeSliceRecordId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_CurrencyExchangeRate"}, "type": "cds.Integer64", "key": true}, "_CurrencyExchangeRate": {"type": "cds.Association", "target": "CurrencyExchangeRate", "cardinality": {"max": 1}, "on": [{"ref": ["_CurrencyExchangeRate", "id"]}, "=", {"ref": ["_id"]}]}, "externalName": {"type": "cds.String", "@EndUserText.label": "Name", "length": 255}, "effectiveStatus": {"type": "cds.String", "@EndUserText.label": "Status", "length": 128}, "validFrom": {"type": "cds.Date", "@EndUserText.label": "Effective as of"}, "validTo": {"type": "cds.Date", "@EndUserText.label": "Effective End Date"}, "currencyExchangeRateType": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueId"}, {"referencedEntityType": "sap.sf.extensibility:CurrencyExchangeRateTypePickList", "referencedPropertyType": "sap.sf.extensibility:CurrencyExchangeRateTypePickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Type of Currency Exchange Rate"}, "sourceCurrency": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:currency", "referencedPropertyType": "sap.sf.foundationobjects:currencyId"}], "@EndUserText.label": "Id"}, "sourceCurrencyId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Currency", "referencedPropertyType": "sap.sf.foundationobjects:CurrencyId"}], "type": "cds.Integer64", "@EndUserText.label": "Source Currency"}, "targetCurrencyId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Currency", "referencedPropertyType": "sap.sf.foundationobjects:CurrencyId"}], "type": "cds.Integer64", "@EndUserText.label": "Target Currency"}, "exchangeRate": {"type": "cds.Decimal", "@EndUserText.label": "Exchange Rate", "precision": 34, "scale": 17}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:CurrencyExchangeRateTimeSliceRecordId"}, "createdAssignment": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "@EndUserText.label": "Id"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "entityOID": {"type": "cds.String", "@EndUserText.label": "entityOID", "length": 128}, "texts": {"type": "cds.Composition", "target": "CurrencyExchangeRateText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_recordId"]}, "=", {"ref": ["recordId"]}]}}}, "CurrencyExchangeRateText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized value for translatable data type field", "@EntityRelationship.entityType": "sap.sf.foundationobjects:CurrencyExchangeRateText", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_CurrencyExchangeRateTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_CurrencyExchangeRateTimeSlice": {"type": "cds.Association", "target": "CurrencyExchangeRateTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_CurrencyExchangeRateTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "length": 128, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:CurrencyExchangeRateTextLocale"}, "externalName": {"type": "cds.String", "@EndUserText.label": "Name", "length": 255}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "default"}}}}}