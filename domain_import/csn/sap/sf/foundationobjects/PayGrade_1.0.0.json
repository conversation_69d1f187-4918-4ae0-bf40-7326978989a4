{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/paystructure/v1/metadata/payGrade", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "PayGrade", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"PayGrade": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Pay Grade", "@ODM.entityName": "PayGrade", "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayGrade", "@EntityRelationship.temporalIds": [{"name": "PayGrade", "propertyTypes": ["sap.sf.foundationobjects:PayGradeId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Internal Code", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayGradeId"}, "externalCode": {"type": "cds.String", "@EndUserText.label": "Pay Grade ID", "length": 32}, "timeslices": {"type": "cds.Composition", "target": "PayGradeTimeslice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Pay Grade", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "PayGradeTimeslice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Pay Grade", "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayGradeTimeslice", "@EntityRelationship.temporalIds": [{"name": "PayGradeTimeslice", "propertyTypes": ["sap.sf.foundationobjects:PayGradeTimesliceId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PayGrade"}, "type": "cds.Integer64", "key": true}, "_PayGrade": {"type": "cds.Association", "target": "PayGrade", "cardinality": {"max": 1}, "on": [{"ref": ["_PayGrade", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Object ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayGradeTimesliceId", "@ObjectModel.text.association": {"=": "texts"}}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 32}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128}, "status": {"type": "cds.String", "@EndUserText.label": "Status"}, "startDate": {"type": "cds.Date", "@EndUserText.label": "Start Date"}, "endDate": {"type": "cds.Date", "@EndUserText.label": "End Date"}, "paygradeLevel": {"type": "cds.Integer64", "@EndUserText.label": "Pay Grade Level", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}, "texts": {"type": "cds.Composition", "target": "PayGradeText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "texts", "on": [{"ref": ["texts", "_id"]}, "=", {"ref": ["id"]}]}}}, "PayGradeText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "PayGradeText", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayGradeText", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PayGradeTimeslice"}, "type": "cds.Integer64", "key": true}, "_PayGradeTimeslice": {"type": "cds.Association", "target": "PayGradeTimeslice", "cardinality": {"max": 1}, "on": [{"ref": ["_PayGradeTimeslice", "id"]}, "=", {"ref": ["_id"]}]}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 32, "@Semantics.text": true}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128, "@Semantics.text": true}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayGradeTextLocale", "@Semantics.language": true}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "<PERSON><PERSON><PERSON>"}}}}}