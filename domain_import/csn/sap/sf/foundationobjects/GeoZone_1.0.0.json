{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/v1/metadata/geoZone", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "GeoZone", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"GeoZone": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Geo Zone", "@EntityRelationship.entityType": "sap.sf.foundationobjects:GeoZone", "@EntityRelationship.temporalIds": [{"name": "GeoZone", "propertyTypes": ["sap.sf.foundationobjects:GeoZoneId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Internal Code", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:GeoZoneId"}, "externalCode": {"type": "cds.String", "@EndUserText.label": "Code", "length": 32}, "timeslices": {"type": "cds.Composition", "target": "GeoZoneTimeslice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Geo Zone", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "GeoZoneTimeslice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Geo Zone", "@EntityRelationship.entityType": "sap.sf.foundationobjects:GeoZoneTimeslice", "@EntityRelationship.temporalIds": [{"name": "GeoZoneTimeslice", "propertyTypes": ["sap.sf.foundationobjects:GeoZoneTimesliceId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_GeoZone"}, "type": "cds.Integer64", "key": true}, "_GeoZone": {"type": "cds.Association", "target": "GeoZone", "cardinality": {"max": 1}, "on": [{"ref": ["_GeoZone", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Object ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:GeoZoneTimesliceId", "@ObjectModel.text.association": {"=": "texts"}}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 32}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128}, "status": {"type": "cds.String", "@EndUserText.label": "Status"}, "startDate": {"type": "cds.Date", "@EndUserText.label": "Start Date"}, "endDate": {"type": "cds.Date", "@EndUserText.label": "End Date"}, "adjustmentPercentage": {"type": "cds.Decimal", "@EndUserText.label": "Adjustment Percentage", "precision": 34, "scale": 2}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}, "texts": {"type": "cds.Composition", "target": "GeoZoneText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "texts", "on": [{"ref": ["texts", "_id"]}, "=", {"ref": ["id"]}]}}}, "GeoZoneText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "GeoZoneText", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.foundationobjects:GeoZoneText", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_GeoZoneTimeslice"}, "type": "cds.Integer64", "key": true}, "_GeoZoneTimeslice": {"type": "cds.Association", "target": "GeoZoneTimeslice", "cardinality": {"max": 1}, "on": [{"ref": ["_GeoZoneTimeslice", "id"]}, "=", {"ref": ["_id"]}]}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 32, "@Semantics.text": true}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128, "@Semantics.text": true}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:GeoZoneTextLocale", "@Semantics.language": true}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "<PERSON><PERSON><PERSON>"}}}}}