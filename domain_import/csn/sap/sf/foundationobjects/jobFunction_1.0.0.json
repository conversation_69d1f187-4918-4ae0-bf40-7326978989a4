{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/jobstructure/v1/metadata/jobFunction", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "JobFunction", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"JobFunction": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Job Function", "@ODM.entityName": "JobFunction", "@EntityRelationship.entityType": "sap.sf.foundationobjects:JobFunction", "@EntityRelationship.temporalIds": [{"name": "JobFunction", "propertyTypes": ["sap.sf.foundationobjects:JobFunctionId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:JobFunctionId"}, "externalCode": {"type": "cds.String", "@EndUserText.label": "Job Function ID", "length": 32}, "timeslices": {"type": "cds.Composition", "target": "JobFunctionTimeSlice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Timeslices", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "JobFunctionTimeSlice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "JobFunctionTimeSlice", "@EntityRelationship.entityType": "sap.sf.foundationobjects:JobFunctionTimeSlice", "@EntityRelationship.temporalIds": [{"name": "JobFunctionTimeSlice", "propertyTypes": ["sap.sf.foundationobjects:JobFunctionTimeSliceRecordId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_JobFunction"}, "type": "cds.Integer64", "key": true}, "_JobFunction": {"type": "cds.Association", "target": "JobFunction", "cardinality": {"max": 1}, "on": [{"ref": ["_JobFunction", "id"]}, "=", {"ref": ["_id"]}]}, "validFrom": {"type": "cds.Date", "@EndUserText.label": "Effective as of"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 90}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128}, "effectiveStatus": {"type": "cds.String", "@EndUserText.label": "Status", "length": 128}, "parentJobFunction": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:jobFunction", "referencedPropertyType": "sap.sf.foundationobjects:jobFunctionId"}], "@EndUserText.label": "Id"}, "parentJobFunctionId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:JobFunction", "referencedPropertyType": "sap.sf.foundationobjects:JobFunctionId"}], "type": "cds.Integer64", "@EndUserText.label": "Parent Job Function"}, "jobFunctionType": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueId"}, {"referencedEntityType": "sap.sf.extensibility:JobFunctionTypePickList", "referencedPropertyType": "sap.sf.extensibility:JobFunctionTypePickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Job Function Type"}, "autoCreated": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "autoCreated"}, "validTo": {"type": "cds.Date", "@EndUserText.label": "Effective End Date"}, "createdAssignment": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "@EndUserText.label": "Id"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:JobFunctionTimeSliceRecordId"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "texts": {"type": "cds.Composition", "target": "JobFunctionText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_recordId"]}, "=", {"ref": ["recordId"]}]}}}, "JobFunctionText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized value for translatable data type field", "@EntityRelationship.entityType": "sap.sf.foundationobjects:JobFunctionText", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_JobFunctionTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_JobFunctionTimeSlice": {"type": "cds.Association", "target": "JobFunctionTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_JobFunctionTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "length": 128, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:JobFunctionTextLocale"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 90}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "default"}}}}}