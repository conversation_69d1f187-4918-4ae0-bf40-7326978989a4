{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/jobstructure/v1/metadata/jobClassification", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "JobClassification", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"JobClassification": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Job Classification ", "@ODM.entityName": "JobClassification", "@ODM.oid": "entityOID", "@EntityRelationship.entityType": "sap.sf.foundationobjects:JobClassification", "@EntityRelationship.temporalIds": [{"name": "JobClassification", "propertyTypes": ["sap.sf.foundationobjects:JobClassificationId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:JobClassificationId"}, "entityOID": {"type": "cds.String", "@EndUserText.label": "entityOID", "length": 128}, "externalCode": {"type": "cds.String", "@EndUserText.label": "Job Code", "length": 32}, "timeslices": {"type": "cds.Composition", "target": "JobClassificationTimeSlice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Timeslices", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "JobClassificationTimeSlice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "JobClassificationTimeSlice", "@EntityRelationship.entityType": "sap.sf.foundationobjects:JobClassificationTimeSlice", "@EntityRelationship.temporalIds": [{"name": "JobClassificationTimeSlice", "propertyTypes": ["sap.sf.foundationobjects:JobClassificationTimeSliceRecordId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_JobClassification"}, "type": "cds.Integer64", "key": true}, "_JobClassification": {"type": "cds.Association", "target": "JobClassification", "cardinality": {"max": 1}, "on": [{"ref": ["_JobClassification", "id"]}, "=", {"ref": ["_id"]}]}, "validFrom": {"type": "cds.Date", "@EndUserText.label": "Effective as of"}, "name": {"type": "cds.String", "@EndUserText.label": "Job Title", "length": 90}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 4000}, "effectiveStatus": {"type": "cds.String", "@EndUserText.label": "Status", "length": 128}, "workerCompCode": {"type": "cds.String", "@EndUserText.label": "Workers’ Comp Code", "length": 32}, "parentJobClassification": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:jobClassification", "referencedPropertyType": "sap.sf.foundationobjects:jobClassificationId"}], "@EndUserText.label": "Id"}, "parentJobClassificationId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:JobClassification", "referencedPropertyType": "sap.sf.foundationobjects:JobClassificationId"}], "type": "cds.Integer64", "@EndUserText.label": "Parent Job Classification "}, "defaultJobLevel": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueId"}, {"referencedEntityType": "sap.sf.extensibility:JobLevelPickList", "referencedPropertyType": "sap.sf.extensibility:JobLevelPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "defaultJobLevel"}, "standardWeeklyHours": {"type": "cds.Decimal", "@EndUserText.label": "Standard Weekly Hours", "precision": 34, "scale": 17}, "regularTemporary": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueId"}, {"referencedEntityType": "sap.sf.extensibility:RegularTemporaryPickList", "referencedPropertyType": "sap.sf.extensibility:RegularTemporaryPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Regular/Temporary"}, "defaultEmployeeClass": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueId"}, {"referencedEntityType": "sap.sf.extensibility:EmployeeClassPickList", "referencedPropertyType": "sap.sf.extensibility:EmployeeClassPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "defaultEmployeeClass"}, "fulltimeEmployee": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Full Time Employee"}, "defaultSupervisorLevel": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueId"}, {"referencedEntityType": "sap.sf.extensibility:EmployeeClassPickList", "referencedPropertyType": "sap.sf.extensibility:EmployeeClassPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "defaultSupervisorLevel"}, "payGrade": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:payGrade", "referencedPropertyType": "sap.sf.foundationobjects:payGradeId"}], "@EndUserText.label": "Id"}, "payGradeId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:PayGrade", "referencedPropertyType": "sap.sf.foundationobjects:PayGradeId"}], "type": "cds.Integer64", "@EndUserText.label": "Pay Grade"}, "jobFunction": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:jobFunction", "referencedPropertyType": "sap.sf.foundationobjects:jobFunctionId"}], "@EndUserText.label": "Id"}, "jobFunctionId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:JobFunction", "referencedPropertyType": "sap.sf.foundationobjects:JobFunctionId"}], "type": "cds.Integer64", "@EndUserText.label": "Job Function"}, "autoCreated": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "autoCreated"}, "validTo": {"type": "cds.Date", "@EndUserText.label": "Effective End Date"}, "createdAssignment": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "@EndUserText.label": "Id"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:JobClassificationTimeSliceRecordId"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "texts": {"type": "cds.Composition", "target": "JobClassificationText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_recordId"]}, "=", {"ref": ["recordId"]}]}}}, "JobClassificationText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized value for translatable data type field", "@EntityRelationship.entityType": "sap.sf.foundationobjects:JobClassificationText", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_JobClassificationTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_JobClassificationTimeSlice": {"type": "cds.Association", "target": "JobClassificationTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_JobClassificationTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "length": 128, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:JobClassificationTextLocale"}, "name": {"type": "cds.String", "@EndUserText.label": "Job Title", "length": 90}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 4000}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "default"}}}}}