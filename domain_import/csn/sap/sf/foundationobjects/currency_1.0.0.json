{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/v1/metadata/currency", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "<PERSON><PERSON><PERSON><PERSON>", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"Currency": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "<PERSON><PERSON><PERSON><PERSON>", "@ODM.entityName": "CurrencyCode", "@EntityRelationship.entityType": "sap.sf.foundationobjects:Currency", "@EntityRelationship.temporalIds": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "propertyTypes": ["sap.sf.foundationobjects:CurrencyId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:CurrencyId"}, "code": {"type": "cds.String", "@EndUserText.label": "Currency Code", "length": 128}, "timeslices": {"type": "cds.Composition", "target": "CurrencyTimeSlice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Timeslices", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "CurrencyTimeSlice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "CurrencyTimeSlice", "@EntityRelationship.entityType": "sap.sf.foundationobjects:CurrencyTimeSlice", "@EntityRelationship.temporalIds": [{"name": "CurrencyTimeSlice", "propertyTypes": ["sap.sf.foundationobjects:CurrencyTimeSliceRecordId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_C<PERSON>rency"}, "type": "cds.Integer64", "key": true}, "_Currency": {"type": "cds.Association", "target": "<PERSON><PERSON><PERSON><PERSON>", "cardinality": {"max": 1}, "on": [{"ref": ["_C<PERSON>rency", "id"]}, "=", {"ref": ["_id"]}]}, "externalName": {"type": "cds.String", "@EndUserText.label": "<PERSON><PERSON><PERSON><PERSON>", "length": 255}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 128}, "validFrom": {"type": "cds.Date", "@EndUserText.label": "Effective as of"}, "validTo": {"type": "cds.Date", "@EndUserText.label": "Effective until"}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 255}, "defaultDecimals": {"type": "cds.Integer64", "@EndUserText.label": "<PERSON><PERSON><PERSON>", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "symbol": {"type": "cds.String", "@EndUserText.label": "Symbol", "length": 255}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:CurrencyTimeSliceRecordId"}, "createdAssignment": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "@EndUserText.label": "Id"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "texts": {"type": "cds.Composition", "target": "CurrencyText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_recordId"]}, "=", {"ref": ["recordId"]}]}}}, "CurrencyText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized value for translatable data type field", "@EntityRelationship.entityType": "sap.sf.foundationobjects:CurrencyText", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_CurrencyTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_CurrencyTimeSlice": {"type": "cds.Association", "target": "CurrencyTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_CurrencyTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "length": 128, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:CurrencyTextLocale"}, "externalName": {"type": "cds.String", "@EndUserText.label": "<PERSON><PERSON><PERSON><PERSON>", "length": 255}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 255}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "default"}}}}}