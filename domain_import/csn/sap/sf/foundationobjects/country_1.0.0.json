{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/v1/metadata/country", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "Country", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"Country": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Country/Region", "@ODM.entityName": "CountryCode", "@EntityRelationship.entityType": "sap.sf.foundationobjects:Country", "@EntityRelationship.temporalIds": [{"name": "Country", "propertyTypes": ["sap.sf.foundationobjects:CountryId", "sap.sf.foundationobjects:CountryCode"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:CountryId"}, "code": {"type": "cds.String", "@EndUserText.label": "Country/Region Code (3 char)", "length": 128}, "timeslices": {"type": "cds.Composition", "target": "CountryTimeSlice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Timeslices", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "CountryTimeSlice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "CountryTimeSlice", "@EntityRelationship.entityType": "sap.sf.foundationobjects:CountryTimeSlice", "@EntityRelationship.temporalIds": [{"name": "CountryTimeSlice", "propertyTypes": ["sap.sf.foundationobjects:CountryTimeSliceRecordId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Country"}, "type": "cds.Integer64", "key": true}, "_Country": {"type": "cds.Association", "target": "Country", "cardinality": {"max": 1}, "on": [{"ref": ["_Country", "id"]}, "=", {"ref": ["_id"]}]}, "externalName": {"type": "cds.String", "@EndUserText.label": "Country/Region", "length": 255}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 128}, "validFrom": {"type": "cds.Date", "@EndUserText.label": "Effective as of"}, "validTo": {"type": "cds.Date", "@EndUserText.label": "Effective until"}, "twoCharCountryCode": {"type": "cds.String", "@EndUserText.label": "Country/Region Code (2 char)", "length": 255}, "numericCountryCode": {"type": "cds.Integer64", "@EndUserText.label": "Country/Region Code (numeric-3)", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "currency": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:currency", "referencedPropertyType": "sap.sf.foundationobjects:currencyId"}], "@EndUserText.label": "Id"}, "currencyId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Currency", "referencedPropertyType": "sap.sf.foundationobjects:CurrencyId"}], "type": "cds.Integer64", "@EndUserText.label": "<PERSON><PERSON><PERSON><PERSON>"}, "territoryId": {"type": "cds.Integer64", "@EndUserText.label": "territoryId", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "isSetByMigrate": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "isSetByMigrate"}, "isDRMEnabled": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Data Retention Enabled"}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:CountryTimeSliceRecordId"}, "createdAssignment": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "@EndUserText.label": "Id"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "texts": {"type": "cds.Composition", "target": "CountryText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_recordId"]}, "=", {"ref": ["recordId"]}]}}}, "CountryText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized value for translatable data type field", "@EntityRelationship.entityType": "sap.sf.foundationobjects:CountryText", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_CountryTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_CountryTimeSlice": {"type": "cds.Association", "target": "CountryTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_CountryTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "length": 128, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:CountryTextLocale"}, "externalName": {"type": "cds.String", "@EndUserText.label": "Country/Region", "length": 255}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "default"}}}}}