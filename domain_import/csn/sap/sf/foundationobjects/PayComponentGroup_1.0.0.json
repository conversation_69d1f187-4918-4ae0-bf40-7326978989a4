{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/compensation/v1/metadata/payComponentGroup", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "PayComponentGroup", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"PayComponentGroup": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Pay Component Group", "@ODM.entityName": "PayComponentGroup", "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayComponentGroup", "@EntityRelationship.temporalIds": [{"name": "PayComponentGroup", "propertyTypes": ["sap.sf.foundationobjects:PayComponentGroupId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Internal Code", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayComponentGroupId"}, "externalCode": {"type": "cds.String", "@EndUserText.label": "Pay Component Group ID", "length": 32}, "timeslices": {"type": "cds.Composition", "target": "PayComponentGroupTimeslice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Pay Component Group", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "PayComponentGroupTimeslice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Pay Component Group", "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayComponentGroupTimeslice", "@EntityRelationship.temporalIds": [{"name": "PayComponentGroupTimeslice", "propertyTypes": ["sap.sf.foundationobjects:PayComponentGroupTimesliceId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PayComponentGroup"}, "type": "cds.Integer64", "key": true}, "_PayComponentGroup": {"type": "cds.Association", "target": "PayComponentGroup", "cardinality": {"max": 1}, "on": [{"ref": ["_PayComponentGroup", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Object ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayComponentGroupTimesliceId", "@ObjectModel.text.association": {"=": "texts"}}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 32}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128}, "status": {"type": "cds.String", "@EndUserText.label": "Status"}, "startDate": {"type": "cds.Date", "@EndUserText.label": "Start Date"}, "endDate": {"type": "cds.Date", "@EndUserText.label": "End Date"}, "currency": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Currency", "referencedPropertyType": "sap.sf.foundationobjects:CurrencyId"}], "type": "cds.String", "@EndUserText.label": "<PERSON><PERSON><PERSON><PERSON>"}, "showOnCompUI": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Display on Comp UI"}, "useForComparatioCalc": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Use for Comparatio Calculation"}, "useForRangePenetration": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Use for Range Penetration"}, "sortOrder": {"type": "cds.Decimal", "@EndUserText.label": "Sort Order", "precision": 34, "scale": 3}, "maxFractionDigits": {"type": "cds.Integer64", "@EndUserText.label": "Maximum Decimal Places", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}, "x-payComponents": {"type": "cds.Association", "target": "PayComponentGroupPayComponents", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "x-payComponents", "on": [{"ref": ["x-payComponents", "payComponentGroupTimesliceId"]}, "=", {"ref": ["id"]}], "@ObjectModel.custom": true}, "texts": {"type": "cds.Composition", "target": "PayComponentGroupText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "texts", "on": [{"ref": ["texts", "_id"]}, "=", {"ref": ["id"]}]}}}, "PayComponentGroupPayComponents": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "PayComponentGroupPayComponents", "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayComponentGroupPayComponents", "elements": {"payComponentGroupTimesliceId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PayComponentGroupTimeslice"}, "type": "cds.Integer64", "key": true}, "_PayComponentGroupTimeslice": {"type": "cds.Association", "target": "PayComponentGroupTimeslice", "cardinality": {"max": 1}, "on": [{"ref": ["_PayComponentGroupTimeslice", "id"]}, "=", {"ref": ["payComponentGroupTimesliceId"]}]}, "id": {"type": "cds.Integer64", "key": true, "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:payComponent", "referencedPropertyType": "sap.sf.foundationobjects:payComponentId"}], "@EndUserText.label": "Id"}}}, "PayComponentGroupText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "PayComponentGroupText", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayComponentGroupText", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PayComponentGroupTimeslice"}, "type": "cds.Integer64", "key": true}, "_PayComponentGroupTimeslice": {"type": "cds.Association", "target": "PayComponentGroupTimeslice", "cardinality": {"max": 1}, "on": [{"ref": ["_PayComponentGroupTimeslice", "id"]}, "=", {"ref": ["_id"]}]}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 32, "@Semantics.text": true}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128, "@Semantics.text": true}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayComponentGroupTextLocale", "@Semantics.language": true}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "<PERSON><PERSON><PERSON>"}}}}}