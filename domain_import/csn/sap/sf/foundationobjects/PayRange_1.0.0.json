{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/paystructure/v1/metadata/payRange", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "PayRange", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"PayRange": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Pay Range", "@ODM.entityName": "PayRange", "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayRange", "@EntityRelationship.temporalIds": [{"name": "PayRange", "propertyTypes": ["sap.sf.foundationobjects:PayRangeId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Internal Code", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayRangeId"}, "externalCode": {"type": "cds.String", "@EndUserText.label": "Pay Range ID", "length": 32}, "timeslices": {"type": "cds.Composition", "target": "PayRangeTimeslice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Pay Range", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "PayRangeTimeslice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Pay Range", "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayRangeTimeslice", "@EntityRelationship.temporalIds": [{"name": "PayRangeTimeslice", "propertyTypes": ["sap.sf.foundationobjects:PayRangeTimesliceId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PayRange"}, "type": "cds.Integer64", "key": true}, "_PayRange": {"type": "cds.Association", "target": "PayRange", "cardinality": {"max": 1}, "on": [{"ref": ["_PayRange", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Object ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayRangeTimesliceId", "@ObjectModel.text.association": {"=": "texts"}}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 32}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128}, "status": {"type": "cds.String", "@EndUserText.label": "Status"}, "startDate": {"type": "cds.Date", "@EndUserText.label": "Start Date"}, "endDate": {"type": "cds.Date", "@EndUserText.label": "End Date"}, "currency": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Currency", "referencedPropertyType": "sap.sf.foundationobjects:CurrencyId"}], "type": "cds.String", "@EndUserText.label": "<PERSON><PERSON><PERSON><PERSON>", "length": 45}, "frequencyCode": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Frequency", "referencedPropertyType": "sap.sf.foundationobjects:FrequencyId"}], "type": "cds.Integer64", "@EndUserText.label": "Frequency", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "minimumPay": {"type": "cds.Decimal", "@EndUserText.label": "Minimum Pay", "precision": 34, "scale": 3}, "maximumPay": {"type": "cds.Decimal", "@EndUserText.label": "Maximum Pay", "precision": 34, "scale": 3}, "midPoint": {"type": "cds.Decimal", "@EndUserText.label": "Mid Point", "precision": 34, "scale": 3}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}, "x-geoZone": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:GeoZone", "referencedPropertyType": "sap.sf.foundationobjects:GeoZoneId"}], "type": "cds.Integer64", "@EndUserText.label": "x-geoZone", "@ObjectModel.custom": true}, "x-payGrade": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:PayGrade", "referencedPropertyType": "sap.sf.foundationobjects:PayGradeId"}], "type": "cds.Integer64", "@EndUserText.label": "x-payGrade", "@ObjectModel.custom": true}, "x-legalEntity": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:LegalEntity", "referencedPropertyType": "sap.sf.foundationobjects:LegalEntityId"}], "type": "cds.Integer64", "@EndUserText.label": "x-legalEntity", "@ObjectModel.custom": true}, "texts": {"type": "cds.Composition", "target": "PayRangeText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "texts", "on": [{"ref": ["texts", "_id"]}, "=", {"ref": ["id"]}]}}}, "PayRangeText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "PayRangeText", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayRangeText", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PayRangeTimeslice"}, "type": "cds.Integer64", "key": true}, "_PayRangeTimeslice": {"type": "cds.Association", "target": "PayRangeTimeslice", "cardinality": {"max": 1}, "on": [{"ref": ["_PayRangeTimeslice", "id"]}, "=", {"ref": ["_id"]}]}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 32, "@Semantics.text": true}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128, "@Semantics.text": true}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayRangeTextLocale", "@Semantics.language": true}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "<PERSON><PERSON><PERSON>"}}}}}