{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/orgstructure/v1/metadata/businessUnit", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "BusinessUnit", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"BusinessUnit": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Business Unit", "@ODM.entityName": "OrganizationalUnit", "@ODM.oid": "entityOID", "@EntityRelationship.entityType": "sap.sf.foundationobjects:BusinessUnit", "@EntityRelationship.temporalIds": [{"name": "BusinessUnit", "propertyTypes": ["sap.sf.foundationobjects:BusinessUnitId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:BusinessUnitId"}, "entityOID": {"type": "cds.String", "@EndUserText.label": "entityOID", "length": 128}, "externalCode": {"type": "cds.String", "@EndUserText.label": "Code", "length": 32}, "timeslices": {"type": "cds.Composition", "target": "BusinessUnitTimeSlice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Timeslices", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "BusinessUnitTimeSlice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "BusinessUnitTimeSlice", "@EntityRelationship.entityType": "sap.sf.foundationobjects:BusinessUnitTimeSlice", "@EntityRelationship.temporalIds": [{"name": "BusinessUnitTimeSlice", "propertyTypes": ["sap.sf.foundationobjects:BusinessUnitTimeSliceRecordId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_BusinessUnit"}, "type": "cds.Integer64", "key": true}, "_BusinessUnit": {"type": "cds.Association", "target": "BusinessUnit", "cardinality": {"max": 1}, "on": [{"ref": ["_BusinessUnit", "id"]}, "=", {"ref": ["_id"]}]}, "validFrom": {"type": "cds.Date", "@EndUserText.label": "Effective as of"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 90}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128}, "effectiveStatus": {"type": "cds.String", "@EndUserText.label": "Status", "length": 128}, "headOfUnit": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "@EndUserText.label": "Id"}, "headOfUnitId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Head of Unit", "length": 100}, "autoCreated": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "autoCreated"}, "validTo": {"type": "cds.Date", "@EndUserText.label": "Effective End Date"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:BusinessUnitTimeSliceRecordId"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "texts": {"type": "cds.Composition", "target": "BusinessUnitText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_recordId"]}, "=", {"ref": ["recordId"]}]}}}, "BusinessUnitText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized value for translatable data type field", "@EntityRelationship.entityType": "sap.sf.foundationobjects:BusinessUnitText", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_BusinessUnitTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_BusinessUnitTimeSlice": {"type": "cds.Association", "target": "BusinessUnitTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_BusinessUnitTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "length": 128, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:BusinessUnitTextLocale"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 90}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "default"}}}}}