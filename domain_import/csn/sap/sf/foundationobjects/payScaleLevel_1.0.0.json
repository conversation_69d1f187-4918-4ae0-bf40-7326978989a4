{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/paystructure/v1/metadata/payScaleLevel", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "PayScaleLevel", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"PayScaleLevel": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Pay Scale Level", "@ODM.entityName": "PayScaleLevel", "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayScaleLevel", "@EntityRelationship.temporalIds": [{"name": "PayScaleLevel", "propertyTypes": ["sap.sf.foundationobjects:PayScaleLevelId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayScaleLevelId"}, "code": {"type": "cds.String", "@EndUserText.label": "Code", "length": 128}, "timeslices": {"type": "cds.Composition", "target": "PayScaleLevelTimeSlice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Timeslices", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "PayScaleLevelTimeSlice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "PayScaleLevelTimeSlice", "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayScaleLevelTimeSlice", "@EntityRelationship.temporalIds": [{"name": "PayScaleLevelTimeSlice", "propertyTypes": ["sap.sf.foundationobjects:PayScaleLevelTimeSliceRecordId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PayScaleLevel"}, "type": "cds.Integer64", "key": true}, "_PayScaleLevel": {"type": "cds.Association", "target": "PayScaleLevel", "cardinality": {"max": 1}, "on": [{"ref": ["_PayScaleLevel", "id"]}, "=", {"ref": ["_id"]}]}, "payScaleLevel": {"type": "cds.String", "@EndUserText.label": "Pay Scale Level", "length": 128}, "externalName": {"type": "cds.String", "@EndUserText.label": "External Name", "length": 255}, "validFrom": {"type": "cds.Date", "@EndUserText.label": "Effective Start Date"}, "validTo": {"type": "cds.Date", "@EndUserText.label": "Effective End Date"}, "nextPayScaleLevel": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:payScaleLevel", "referencedPropertyType": "sap.sf.foundationobjects:payScaleLevelId"}], "@EndUserText.label": "Id"}, "nextPayScaleLevelId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:PayScaleLevel", "referencedPropertyType": "sap.sf.foundationobjects:PayScaleLevelId"}], "type": "cds.Integer64", "@EndUserText.label": "Next Pay Scale Level"}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayScaleLevelTimeSliceRecordId"}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "createdAssignment": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "@EndUserText.label": "Id"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "toPayScalePayComponents": {"type": "cds.Composition", "target": "PayScalePayComponent", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Pay Component Assignment", "on": [{"ref": ["toPayScalePayComponents", "_recordId"]}, "=", {"ref": ["recordId"]}]}, "payScaleGroup": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:payScaleGroup", "referencedPropertyType": "sap.sf.foundationobjects:payScaleGroupId"}], "@EndUserText.label": "Id"}, "payScaleGroupId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:PayScaleGroup", "referencedPropertyType": "sap.sf.foundationobjects:PayScaleGroupId"}], "type": "cds.Integer64", "@EndUserText.label": "Pay Scale Group"}, "texts": {"type": "cds.Composition", "target": "PayScaleLevelText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_recordId"]}, "=", {"ref": ["recordId"]}]}}}, "PayScalePayComponent": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "PayScalePayComponent", "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayScalePayComponent", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PayScaleLevelTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_PayScaleLevelTimeSlice": {"type": "cds.Association", "target": "PayScaleLevelTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_PayScaleLevelTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "code": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:payComponent", "referencedPropertyType": "sap.sf.foundationobjects:payComponentId"}], "@EndUserText.label": "Id"}, "codeId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:PayComponent", "referencedPropertyType": "sap.sf.foundationobjects:PayComponentId"}], "type": "cds.Integer64", "@EndUserText.label": "Pay Component"}, "amount": {"type": "cds.Decimal", "@EndUserText.label": "Amount", "precision": 34, "scale": 17}, "currency": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:currency", "referencedPropertyType": "sap.sf.foundationobjects:currencyId"}], "@EndUserText.label": "Id"}, "currencyId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Currency", "referencedPropertyType": "sap.sf.foundationobjects:CurrencyId"}], "type": "cds.Integer64", "@EndUserText.label": "<PERSON><PERSON><PERSON><PERSON>"}, "frequency": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:frequency", "referencedPropertyType": "sap.sf.foundationobjects:frequencyId"}], "@EndUserText.label": "Id"}, "frequencyId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Frequency", "referencedPropertyType": "sap.sf.foundationobjects:FrequencyId"}], "type": "cds.Integer64", "@EndUserText.label": "Frequency"}, "number": {"type": "cds.Decimal", "@EndUserText.label": "Number", "precision": 34, "scale": 17}, "unit": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:unitOfMeasure", "referencedPropertyType": "sap.sf.foundationobjects:unitOfMeasureId"}], "@EndUserText.label": "Id"}, "unitId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:UnitOfMeasure", "referencedPropertyType": "sap.sf.foundationobjects:UnitOfMeasureId"}], "type": "cds.Integer64", "@EndUserText.label": "Unit"}, "rate": {"type": "cds.Decimal", "@EndUserText.label": "Rate", "precision": 34, "scale": 17}, "percentage": {"type": "cds.Decimal", "@EndUserText.label": "Percentage", "precision": 34, "scale": 17}, "entityId": {"type": "cds.String", "@EndUserText.label": "Entity ID", "length": 255}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayScalePayComponentRecordId"}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "objectType": {"type": "cds.String", "@EndUserText.label": "Object Type", "length": 255}, "createdAssignment": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "@EndUserText.label": "Id"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}}}, "PayScaleLevelText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized value for translatable data type field", "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayScaleLevelText", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PayScaleLevelTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_PayScaleLevelTimeSlice": {"type": "cds.Association", "target": "PayScaleLevelTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_PayScaleLevelTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "length": 128, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayScaleLevelTextLocale"}, "externalName": {"type": "cds.String", "@EndUserText.label": "External Name", "length": 255}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "default"}}}}}