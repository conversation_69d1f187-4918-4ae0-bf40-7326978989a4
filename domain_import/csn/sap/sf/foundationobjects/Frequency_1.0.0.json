{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/v1/metadata/frequency", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "Frequency", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"Frequency": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Frequency", "@EntityRelationship.entityType": "sap.sf.foundationobjects:Frequency", "@EntityRelationship.entityIds": [{"name": "Frequency", "propertyTypes": ["sap.sf.foundationobjects:FrequencyId"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Internal Code", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:FrequencyId", "@ObjectModel.text.association": {"=": "texts"}}, "externalCode": {"type": "cds.String", "@EndUserText.label": "Frequency ID", "length": 32}, "payFrequency": {"type": "cds.String", "@EndUserText.label": "Pay Frequency", "length": 256}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 32}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128}, "annualizationFactor": {"type": "cds.Decimal", "@EndUserText.label": "Annualization Factor", "precision": 34, "scale": 3}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "objectId": {"type": "cds.Integer64", "@EndUserText.label": "Object ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}, "texts": {"type": "cds.Composition", "target": "FrequencyText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "texts", "on": [{"ref": ["texts", "_id"]}, "=", {"ref": ["id"]}]}}}, "FrequencyText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "FrequencyText", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.foundationobjects:FrequencyText", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Frequency"}, "type": "cds.Integer64", "key": true}, "_Frequency": {"type": "cds.Association", "target": "Frequency", "cardinality": {"max": 1}, "on": [{"ref": ["_Frequency", "id"]}, "=", {"ref": ["_id"]}]}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 32, "@Semantics.text": true}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128, "@Semantics.text": true}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:FrequencyTextLocale", "@Semantics.language": true}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "<PERSON><PERSON><PERSON>"}}}}}