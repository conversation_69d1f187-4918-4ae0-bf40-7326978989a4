{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/paystructure/v1/metadata/payScaleGroup", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "PayScaleGroup", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"PayScaleGroup": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Pay Scale Group", "@ODM.entityName": "PayScaleGroup", "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayScaleGroup", "@EntityRelationship.entityIds": [{"name": "PayScaleGroup", "propertyTypes": ["sap.sf.foundationobjects:PayScaleGroupId"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayScaleGroupId", "@ObjectModel.text.association": {"=": "texts"}}, "code": {"type": "cds.String", "@EndUserText.label": "Code", "length": 128}, "payScaleGroup": {"type": "cds.String", "@EndUserText.label": "Pay Scale Group", "length": 255}, "externalName": {"type": "cds.String", "@EndUserText.label": "External Name", "length": 255}, "country": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:country", "referencedPropertyType": "sap.sf.foundationobjects:countryId"}], "@EndUserText.label": "Id"}, "countryId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Country", "referencedPropertyType": "sap.sf.foundationobjects:CountryId"}], "type": "cds.Integer64", "@EndUserText.label": "Country/Region"}, "payScaleArea": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:payScaleArea", "referencedPropertyType": "sap.sf.foundationobjects:payScaleAreaId"}], "@EndUserText.label": "Id"}, "payScaleAreaId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:PayScaleArea", "referencedPropertyType": "sap.sf.foundationobjects:PayScaleAreaId"}], "type": "cds.Integer64", "@EndUserText.label": "Pay Scale Area"}, "payScaleType": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:payScaleType", "referencedPropertyType": "sap.sf.foundationobjects:payScaleTypeId"}], "@EndUserText.label": "Id"}, "payScaleTypeId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:PayScaleType", "referencedPropertyType": "sap.sf.foundationobjects:PayScaleTypeId"}], "type": "cds.Integer64", "@EndUserText.label": "Pay Scale Type"}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "createdAssignment": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "@EndUserText.label": "Id"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "texts": {"type": "cds.Composition", "target": "PayScaleGroupText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_id"]}, "=", {"ref": ["id"]}]}}}, "PayScaleGroupText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized value for translatable data type field", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayScaleGroupText", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PayScaleGroup"}, "type": "cds.Integer64", "key": true}, "_PayScaleGroup": {"type": "cds.Association", "target": "PayScaleGroup", "cardinality": {"max": 1}, "on": [{"ref": ["_PayScaleGroup", "id"]}, "=", {"ref": ["_id"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "length": 128, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayScaleGroupTextLocale", "@Semantics.language": true}, "externalName": {"type": "cds.String", "@EndUserText.label": "External Name", "length": 255, "@Semantics.text": true}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "default"}}}}}