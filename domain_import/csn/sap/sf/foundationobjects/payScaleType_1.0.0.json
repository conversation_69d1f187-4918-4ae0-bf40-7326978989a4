{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/paystructure/v1/metadata/payScaleType", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "PayScaleType", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"PayScaleType": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Pay Scale Type", "@ODM.entityName": "PayScaleType", "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayScaleType", "@EntityRelationship.entityIds": [{"name": "PayScaleType", "propertyTypes": ["sap.sf.foundationobjects:PayScaleTypeId"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayScaleTypeId", "@ObjectModel.text.association": {"=": "texts"}}, "code": {"type": "cds.String", "@EndUserText.label": "Code", "length": 128}, "payScaleType": {"type": "cds.String", "@EndUserText.label": "Pay Scale Type", "length": 128}, "externalName": {"type": "cds.String", "@EndUserText.label": "External Name", "length": 128}, "country": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:country", "referencedPropertyType": "sap.sf.foundationobjects:countryId"}], "@EndUserText.label": "Id"}, "countryId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Country", "referencedPropertyType": "sap.sf.foundationobjects:CountryId"}], "type": "cds.Integer64", "@EndUserText.label": "Country/Region"}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "createdAssignment": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "@EndUserText.label": "Id"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "texts": {"type": "cds.Composition", "target": "PayScaleTypeText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_id"]}, "=", {"ref": ["id"]}]}}}, "PayScaleTypeText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized value for translatable data type field", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.foundationobjects:PayScaleTypeText", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PayScaleType"}, "type": "cds.Integer64", "key": true}, "_PayScaleType": {"type": "cds.Association", "target": "PayScaleType", "cardinality": {"max": 1}, "on": [{"ref": ["_PayScaleType", "id"]}, "=", {"ref": ["_id"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "length": 128, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:PayScaleTypeTextLocale", "@Semantics.language": true}, "externalName": {"type": "cds.String", "@EndUserText.label": "External Name", "length": 128, "@Semantics.text": true}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "default"}}}}}