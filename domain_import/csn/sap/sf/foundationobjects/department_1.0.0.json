{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/orgstructure/v1/metadata/department", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "Department", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"Department": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Department", "@ODM.entityName": "OrganizationalUnit", "@ODM.oid": "entityOID", "@EntityRelationship.entityType": "sap.sf.foundationobjects:Department", "@EntityRelationship.temporalIds": [{"name": "Department", "propertyTypes": ["sap.sf.foundationobjects:DepartmentId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:DepartmentId"}, "entityOID": {"type": "cds.String", "@EndUserText.label": "entityOID", "length": 128}, "externalCode": {"type": "cds.String", "@EndUserText.label": "Code", "length": 32}, "timeslices": {"type": "cds.Composition", "target": "DepartmentTimeSlice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Timeslices", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "DepartmentTimeSlice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "DepartmentTimeSlice", "@EntityRelationship.entityType": "sap.sf.foundationobjects:DepartmentTimeSlice", "@EntityRelationship.temporalIds": [{"name": "DepartmentTimeSlice", "propertyTypes": ["sap.sf.foundationobjects:DepartmentTimeSliceRecordId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Department"}, "type": "cds.Integer64", "key": true}, "_Department": {"type": "cds.Association", "target": "Department", "cardinality": {"max": 1}, "on": [{"ref": ["_Department", "id"]}, "=", {"ref": ["_id"]}]}, "validFrom": {"type": "cds.Date", "@EndUserText.label": "Effective as of"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 90}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128}, "effectiveStatus": {"type": "cds.String", "@EndUserText.label": "Status", "length": 128}, "headOfUnit": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "@EndUserText.label": "Id"}, "headOfUnitId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Head of Department", "length": 100}, "parentDepartment": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:department", "referencedPropertyType": "sap.sf.foundationobjects:departmentId"}], "@EndUserText.label": "Id"}, "parentDepartmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Department", "referencedPropertyType": "sap.sf.foundationobjects:DepartmentId"}], "type": "cds.Integer64", "@EndUserText.label": "Parent Department"}, "costCenter": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:costCenter", "referencedPropertyType": "sap.sf.foundationobjects:costCenterId"}], "@EndUserText.label": "Id"}, "costCenterId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:CostCenter", "referencedPropertyType": "sap.sf.foundationobjects:CostCenterId"}], "type": "cds.Integer64", "@EndUserText.label": "Cost Center"}, "autoCreated": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "autoCreated"}, "validTo": {"type": "cds.Date", "@EndUserText.label": "Effective End Date"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:DepartmentTimeSliceRecordId"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "toDepartmentApprenticeDetail": {"type": "cds.Composition", "target": "DepartmentApprenticeDetail", "cardinality": {"max": 1}, "@EndUserText.label": "Apprentice Details", "on": [{"ref": ["toDepartmentApprenticeDetail", "_recordId"]}, "=", {"ref": ["recordId"]}]}, "texts": {"type": "cds.Composition", "target": "DepartmentText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_recordId"]}, "=", {"ref": ["recordId"]}]}}}, "DepartmentApprenticeDetail": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Apprentice Department Details", "@EntityRelationship.entityType": "sap.sf.foundationobjects:DepartmentApprenticeDetail", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_DepartmentTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_DepartmentTimeSlice": {"type": "cds.Association", "target": "DepartmentTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_DepartmentTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "maxNumberOfApprentices": {"type": "cds.Integer64", "@EndUserText.label": "Max. No. of Apprentices", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "apprenticeDepartmentLocation": {"type": "cds.String", "@EndUserText.label": "Location", "length": 255}, "decentralTrainerUserId": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "@EndUserText.label": "Id"}, "decentralTrainerUserIdId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "On-Site supervisor", "length": 100}, "externalCode": {"type": "cds.Integer64", "@EndUserText.label": "externalCode", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "entityId": {"type": "cds.String", "@EndUserText.label": "Entity ID", "length": 255}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:DepartmentApprenticeDetailRecordId"}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "objectType": {"type": "cds.String", "@EndUserText.label": "Object Type", "length": 255}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}}}, "DepartmentText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized value for translatable data type field", "@EntityRelationship.entityType": "sap.sf.foundationobjects:DepartmentText", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_DepartmentTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_DepartmentTimeSlice": {"type": "cds.Association", "target": "DepartmentTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_DepartmentTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "length": 128, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:DepartmentTextLocale"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 90}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "default"}}}}}