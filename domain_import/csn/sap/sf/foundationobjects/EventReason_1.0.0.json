{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/v1/metadata/eventReason", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "EventReason", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"EventReason": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Event Reason", "@ODM.entityName": "EventReasonCode", "@EntityRelationship.entityType": "sap.sf.foundationobjects:EventReason", "@EntityRelationship.temporalIds": [{"name": "EventReason", "propertyTypes": ["sap.sf.foundationobjects:EventReasonId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Internal Code", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:EventReasonId"}, "externalCode": {"type": "cds.String", "@EndUserText.label": "Event Reason ID", "length": 32}, "timeslices": {"type": "cds.Composition", "target": "EventReasonTimeslice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Event Reason", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "EventReasonTimeslice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Event Reason", "@EntityRelationship.entityType": "sap.sf.foundationobjects:EventReasonTimeslice", "@EntityRelationship.temporalIds": [{"name": "EventReasonTimeslice", "propertyTypes": ["sap.sf.foundationobjects:EventReasonTimesliceId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_EventReason"}, "type": "cds.Integer64", "key": true}, "_EventReason": {"type": "cds.Association", "target": "EventReason", "cardinality": {"max": 1}, "on": [{"ref": ["_EventReason", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Object ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:EventReasonTimesliceId", "@ObjectModel.text.association": {"=": "texts"}}, "name": {"type": "cds.String", "@EndUserText.label": "Event Reason Name", "length": 32}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128}, "status": {"type": "cds.String", "@EndUserText.label": "Status"}, "startDate": {"type": "cds.Date", "@EndUserText.label": "Start Date"}, "endDate": {"type": "cds.Date", "@EndUserText.label": "End Date"}, "event": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:eventPickList", "referencedPropertyType": "sap.sf.extensibility:eventPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Event", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "emplStatus": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:employee-statusPickList", "referencedPropertyType": "sap.sf.extensibility:employee-statusPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Employee Status", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "implicitPositionAction": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:positionActionTypePickList", "referencedPropertyType": "sap.sf.extensibility:positionActionTypePickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Follow-Up Activity in Position", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "payrollEvent": {"type": "cds.String", "@EndUserText.label": "Payroll Event", "length": 4}, "includeInWorkExperience": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Display in Internal Job History Portlet"}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}, "texts": {"type": "cds.Composition", "target": "EventReasonText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "texts", "on": [{"ref": ["texts", "_id"]}, "=", {"ref": ["id"]}]}}}, "EventReasonText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "EventReasonText", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.foundationobjects:EventReasonText", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_EventReasonTimeslice"}, "type": "cds.Integer64", "key": true}, "_EventReasonTimeslice": {"type": "cds.Association", "target": "EventReasonTimeslice", "cardinality": {"max": 1}, "on": [{"ref": ["_EventReasonTimeslice", "id"]}, "=", {"ref": ["_id"]}]}, "name": {"type": "cds.String", "@EndUserText.label": "Event Reason Name", "length": 32, "@Semantics.text": true}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 128, "@Semantics.text": true}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:EventReasonTextLocale", "@Semantics.language": true}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "<PERSON><PERSON><PERSON>"}}}}}