{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/foundationobjects/enterprisestructure/v1/metadata/costCenter", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "CostCenter", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.foundationobjects", "document": {"version": "1.0.0"}}, "definitions": {"CostCenter": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Cost Center", "@ODM.entityName": "CostCenter", "@ODM.oid": "entityOID", "@EntityRelationship.entityType": "sap.sf.foundationobjects:CostCenter", "@EntityRelationship.temporalIds": [{"name": "CostCenter", "propertyTypes": ["sap.sf.foundationobjects:CostCenterId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:CostCenterId"}, "entityOID": {"type": "cds.String", "@EndUserText.label": "entityOID", "length": 128}, "externalCode": {"type": "cds.String", "@EndUserText.label": "Code", "length": 50}, "timeslices": {"type": "cds.Composition", "target": "CostCenterTimeSlice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Timeslices", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "CostCenterTimeSlice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "CostCenterTimeSlice", "@EntityRelationship.entityType": "sap.sf.foundationobjects:CostCenterTimeSlice", "@EntityRelationship.temporalIds": [{"name": "CostCenterTimeSlice", "propertyTypes": ["sap.sf.foundationobjects:CostCenterTimeSliceRecordId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_CostCenter"}, "type": "cds.Integer64", "key": true}, "_CostCenter": {"type": "cds.Association", "target": "CostCenter", "cardinality": {"max": 1}, "on": [{"ref": ["_CostCenter", "id"]}, "=", {"ref": ["_id"]}]}, "validFrom": {"type": "cds.Date", "@EndUserText.label": "Effective as of"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 200}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 40}, "effectiveStatus": {"type": "cds.String", "@EndUserText.label": "Status", "length": 128}, "glStatementCode": {"type": "cds.String", "@EndUserText.label": "GL Statement Code", "length": 32}, "parentCostCenter": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:costCenter", "referencedPropertyType": "sap.sf.foundationobjects:costCenterId"}], "@EndUserText.label": "Id"}, "parentCostCenterId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:CostCenter", "referencedPropertyType": "sap.sf.foundationobjects:CostCenterId"}], "type": "cds.Integer64", "@EndUserText.label": "Parent Cost Center"}, "costCenterManager": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "@EndUserText.label": "Id"}, "costCenterManagerId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Cost Center Manager", "length": 100}, "costCenterExternalObjectId": {"type": "cds.String", "@EndUserText.label": "costCenterExternalObjectId", "length": 40}, "autoCreated": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "autoCreated"}, "validTo": {"type": "cds.Date", "@EndUserText.label": "Effective End Date"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:CostCenterTimeSliceRecordId"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "legalEntity": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:legalEntity", "referencedPropertyType": "sap.sf.foundationobjects:legalEntityId"}], "@EndUserText.label": "Id"}, "legalEntityId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:LegalEntity", "referencedPropertyType": "sap.sf.foundationobjects:LegalEntityId"}], "type": "cds.Integer64", "@EndUserText.label": "legalEntity"}, "texts": {"type": "cds.Composition", "target": "CostCenterText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_recordId"]}, "=", {"ref": ["recordId"]}]}}}, "CostCenterText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized value for translatable data type field", "@EntityRelationship.entityType": "sap.sf.foundationobjects:CostCenterText", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_CostCenterTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_CostCenterTimeSlice": {"type": "cds.Association", "target": "CostCenterTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_CostCenterTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "length": 128, "key": true, "@EntityRelationship.propertyType": "sap.sf.foundationobjects:CostCenterTextLocale"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 200}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 40}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "default"}}}}}