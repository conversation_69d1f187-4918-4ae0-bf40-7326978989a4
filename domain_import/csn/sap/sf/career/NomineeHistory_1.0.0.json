{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/succession/v1/metadata/nomineeHist", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.career", "document": {"version": "1.0.0"}}, "definitions": {"NomineeHistory": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "nominee history metadata", "@EntityRelationship.entityType": "sap.sf.career:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@EntityRelationship.entityIds": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "propertyTypes": ["sap.sf.career:NomineeHistoryID"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Nominee History Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:NomineeHistoryID"}, "nomineeId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.career:<PERSON><PERSON><PERSON>", "referencedPropertyType": "sap.sf.career:<PERSON><PERSON>e<PERSON>"}], "type": "cds.Integer64", "@EndUserText.label": "Nominee Id"}, "nominationId": {"type": "cds.Integer64", "@EndUserText.label": "Nomination Id"}, "nomineeUserId": {"type": "cds.String", "@EndUserText.label": "Nominee User Id"}, "proxyUserId": {"type": "cds.String", "@EndUserText.label": "Proxy User Id"}, "changeType": {"type": "cds.String", "@EndUserText.label": "Change Type"}, "readinessValue": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.career:NomineeReadiness", "referencedPropertyType": "sap.sf.career:NomineeReadinessID"}], "type": "cds.String", "@EndUserText.label": "ReadinessValue"}, "note": {"type": "cds.String", "@EndUserText.label": "Note"}, "nomineeStatusId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.career:<PERSON><PERSON>eStatus", "referencedPropertyType": "sap.sf.career:NomineeStatusID"}], "type": "cds.Integer64", "@EndUserText.label": "Nominee Status Id"}, "formDataId": {"type": "cds.Integer64", "@EndUserText.label": "Form Data Id"}, "rank": {"type": "cds.Decimal", "@EndUserText.label": "Rank", "precision": 34, "scale": 1}, "externalCandId": {"type": "cds.Integer64", "@EndUserText.label": "External Candidate Id"}, "emergencyCover": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Emergency Cover"}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Modified At"}, "modifiedBy": {"type": "cds.String", "@EndUserText.label": "Modified By"}, "deleted": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Created At"}}}}}