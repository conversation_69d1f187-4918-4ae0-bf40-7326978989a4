{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/succession/v1/metadata/scmPosition", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "SuccessionPosition", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.career", "document": {"version": "1.0.0"}}, "definitions": {"SuccessionPosition": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "succession position metadata", "@EntityRelationship.entityType": "sap.sf.career:SuccessionPosition", "@EntityRelationship.entityIds": [{"name": "SuccessionPosition", "propertyTypes": ["sap.sf.career:SuccessionPositionID"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "SCM Position Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:SuccessionPositionID"}, "code": {"type": "cds.String", "@EndUserText.label": "Position Code"}, "parentPositionId": {"type": "cds.Integer64", "@EndUserText.label": "Parent Position Id"}, "incumbent": {"type": "cds.String", "@EndUserText.label": "Incumbent"}, "criticalPositionRating": {"type": "cds.Integer64", "@EndUserText.label": "Critical Position Rating"}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created At"}, "createdBy": {"type": "cds.String", "@EndUserText.label": "Created By"}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Modified At"}, "modifiedBy": {"type": "cds.String", "@EndUserText.label": "ModifiedBy"}, "deleted": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Deleted"}}}}}