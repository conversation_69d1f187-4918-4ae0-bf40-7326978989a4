{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/succession/v1/metadata/nominationType", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "NominationType", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.career", "document": {"version": "1.0.0"}}, "definitions": {"NominationType": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "nomination type enum metadata", "@EntityRelationship.entityType": "sap.sf.career:NominationType", "@EntityRelationship.entityIds": [{"name": "NominationType", "propertyTypes": ["sap.sf.career:NominationTypeID"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Nomination Type Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:NominationTypeID"}, "value": {"type": "cds.Integer64", "@EndUserText.label": "Nomination Type Value"}, "nominationValueName": {"type": "cds.String", "@EndUserText.label": "Nomination Type Name"}}}}}