{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/development/v1/metadata/objectiveAudit", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "DevelopmentGoalAudit", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.career", "document": {"version": "1.0.0"}}, "definitions": {"DevelopmentGoalAudit": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Development Objective Audit", "@ODM.entityName": "CareerDevelopmentGoalAudit", "@ODM.oid": "id", "@EntityRelationship.entityType": "sap.sf.career:DevelopmentGoalAudit", "@EntityRelationship.entityIds": [{"name": "DevelopmentGoalAudit", "propertyTypes": ["sap.sf.career:DevelopmentGoalAuditID"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Development goal audit ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:DevelopmentGoalAuditID"}, "objectiveId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.career:DevelopmentGoal", "referencedPropertyType": "sap.sf.career:DevelopmentGoalID"}], "type": "cds.Integer64", "@EndUserText.label": "objectiveId"}, "action": {"type": "cds.Integer64", "@EndUserText.label": "action"}, "sender": {"type": "cds.String", "@EndUserText.label": "sender"}, "recipient": {"type": "cds.String", "@EndUserText.label": "recipient"}, "comment": {"type": "cds.String", "@EndUserText.label": "comment"}, "sendProxy": {"type": "cds.String", "@EndUserText.label": "sendProxy"}, "createdBy": {"type": "cds.String", "@EndUserText.label": "created<PERSON>y"}, "modifiedBy": {"type": "cds.String", "@EndUserText.label": "modifiedBy"}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "createdAt"}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "modifiedAt"}}}}}