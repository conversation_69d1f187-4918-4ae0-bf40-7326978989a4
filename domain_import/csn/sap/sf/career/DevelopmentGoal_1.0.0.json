{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/development/v1/metadata/objective", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "DevelopmentGoal", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.career", "document": {"version": "1.0.0"}}, "definitions": {"DevelopmentGoal": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Development Objective", "@ODM.entityName": "CareerDevelopmentGoal", "@ODM.oid": "id", "@EntityRelationship.entityType": "sap.sf.career:DevelopmentGoal", "@EntityRelationship.entityIds": [{"name": "DevelopmentGoal", "propertyTypes": ["sap.sf.career:DevelopmentGoalID"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Development objective ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:DevelopmentGoalID"}, "competencyGuid": {"type": "cds.String", "@EndUserText.label": "Competency Guid"}, "genaiFieldIds": {"type": "cds.String", "@EndUserText.label": "AI-Generated Field IDs"}, "lastStatusItemId": {"type": "cds.Integer64", "@EndUserText.label": "Last Status Item ID"}, "lastUpdateRequestId": {"type": "cds.Integer64", "@EndUserText.label": "Last Update Request ID"}, "achievement": {"type": "cds.Decimal", "@EndUserText.label": "Achievement", "precision": 34, "scale": 1}, "achievementText": {"type": "cds.String", "@EndUserText.label": "Achievement Text"}, "actual": {"type": "cds.Decimal", "@EndUserText.label": "Actual Value", "precision": 34, "scale": 1}, "category": {"type": "cds.String", "@EndUserText.label": "Category"}, "currentOwner": {"type": "cds.String", "@EndUserText.label": "Current Owner"}, "defgrp": {"type": "cds.String", "@EndUserText.label": "Default Group"}, "dept": {"type": "cds.String", "@EndUserText.label": "Department"}, "description": {"type": "cds.String", "@EndUserText.label": "Description"}, "div": {"type": "cds.String", "@EndUserText.label": "Division"}, "dueDate": {"type": "cds.String", "@EndUserText.label": "Due Date"}, "flag": {"type": "cds.Integer64", "@EndUserText.label": "Flag"}, "guid": {"type": "cds.String", "@EndUserText.label": "Guid"}, "lastModified": {"type": "cds.String", "@EndUserText.label": "Last Modified"}, "loc": {"type": "cds.String", "@EndUserText.label": "Location"}, "masterId": {"type": "cds.Integer64", "@EndUserText.label": "Master ID"}, "metric": {"type": "cds.String", "@EndUserText.label": "Metric"}, "mltAchievementType": {"type": "cds.Integer64", "@EndUserText.label": "MLT Achievement Type"}, "mltScale": {"type": "cds.String", "@EndUserText.label": "MLT Scale"}, "modifier": {"type": "cds.String", "@EndUserText.label": "Modifier"}, "name": {"type": "cds.String", "@EndUserText.label": "Name"}, "numbering": {"type": "cds.String", "@EndUserText.label": "Numbering"}, "parentId": {"type": "cds.Integer64", "@EndUserText.label": "Parent ID"}, "percent": {"type": "cds.Decimal", "@EndUserText.label": "Percent", "precision": 34, "scale": 1}, "periodEnd": {"type": "cds.String", "@EndUserText.label": "Period End"}, "periodStart": {"type": "cds.String", "@EndUserText.label": "Period Start"}, "pos": {"type": "cds.Integer64", "@EndUserText.label": "Position"}, "rating": {"type": "cds.Decimal", "@EndUserText.label": "Rating", "precision": 34, "scale": 1}, "state": {"type": "cds.String", "@EndUserText.label": "State"}, "status": {"type": "cds.Integer64", "@EndUserText.label": "Status"}, "strategic": {"type": "cds.Integer64", "@EndUserText.label": "Strategic"}, "target": {"type": "cds.Decimal", "@EndUserText.label": "Target", "precision": 34, "scale": 1}, "type": {"type": "cds.String", "@EndUserText.label": "Type"}, "weight": {"type": "cds.Decimal", "@EndUserText.label": "Weight", "precision": 34, "scale": 1}, "originalObjectiveId": {"type": "cds.Integer64", "@EndUserText.label": "Original ID"}, "userId": {"type": "cds.String", "@EndUserText.label": "User ID"}, "templateId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.career:DevelopmentGoalTemplate", "referencedPropertyType": "sap.sf.career:DevelopmentGoalTemplateID"}], "type": "cds.Integer64", "@EndUserText.label": "Plan ID"}, "planState": {"type": "cds.String", "@EndUserText.label": "Plan State"}, "createdBy": {"type": "cds.String", "@EndUserText.label": "Created By"}, "lastModifiedBy": {"type": "cds.String", "@EndUserText.label": "Last Modified By"}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created At"}, "lastModifiedTime": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Time"}, "milestones": {"type": "cds.Composition", "target": "ObjectiveMileStone", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Milestones", "on": [{"ref": ["milestones", "_id"]}, "=", {"ref": ["id"]}]}, "metricLookups": {"type": "cds.Composition", "target": "ObjectiveMetricLookup", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Metric Lookups", "on": [{"ref": ["metricLookups", "_id"]}, "=", {"ref": ["id"]}]}, "competencies": {"type": "cds.Composition", "target": "ObjectiveCompetency", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Competencies", "on": [{"ref": ["competencies", "_id"]}, "=", {"ref": ["id"]}]}, "relatedLearningItems": {"type": "cds.Composition", "target": "ObjectiveRelatedLearningItem", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Related Learning Items", "on": [{"ref": ["relatedLearningItems", "_id"]}, "=", {"ref": ["id"]}]}, "createdDateTime": {"type": "cds.DateTime", "@EndUserText.label": "Created At"}}}, "ObjectiveMileStone": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Development Objective Milestone", "@EntityRelationship.entityType": "sap.sf.career:ObjectiveMileStone", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_DevelopmentGoal"}, "type": "cds.Integer64", "key": true}, "_DevelopmentGoal": {"type": "cds.Association", "target": "DevelopmentGoal", "cardinality": {"max": 1}, "on": [{"ref": ["_DevelopmentGoal", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "id", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:ObjectiveMileStoneId"}, "objectiveId": {"type": "cds.Integer64", "@EndUserText.label": "objectiveId"}, "actual": {"type": "cds.String", "@EndUserText.label": "actual"}, "actualNumber": {"type": "cds.Decimal", "@EndUserText.label": "actualNumber", "precision": 34, "scale": 1}, "code": {"type": "cds.String", "@EndUserText.label": "code"}, "customNum1": {"type": "cds.Decimal", "@EndUserText.label": "customNum1", "precision": 34, "scale": 1}, "customNum2": {"type": "cds.Decimal", "@EndUserText.label": "customNum2", "precision": 34, "scale": 1}, "customNum3": {"type": "cds.Decimal", "@EndUserText.label": "customNum3", "precision": 34, "scale": 1}, "description": {"type": "cds.String", "@EndUserText.label": "description"}, "order": {"type": "cds.Integer64", "@EndUserText.label": "order"}, "due": {"type": "cds.String", "@EndUserText.label": "due"}, "flag": {"type": "cds.Integer64", "@EndUserText.label": "flag"}, "lastModified": {"type": "cds.String", "@EndUserText.label": "lastModified"}, "masterId": {"type": "cds.Integer64", "@EndUserText.label": "masterId"}, "modifier": {"type": "cds.String", "@EndUserText.label": "modifier"}, "percent": {"type": "cds.Decimal", "@EndUserText.label": "percent", "precision": 34, "scale": 1}, "rating": {"type": "cds.Decimal", "@EndUserText.label": "rating", "precision": 34, "scale": 1}, "mileScore": {"type": "cds.Decimal", "@EndUserText.label": "mileScore", "precision": 34, "scale": 1}, "mileStart": {"type": "cds.String", "@EndUserText.label": "mileStart"}, "mileTarget": {"type": "cds.String", "@EndUserText.label": "mileTarget"}, "mileWeight": {"type": "cds.Decimal", "@EndUserText.label": "mileWeight", "precision": 34, "scale": 1}}}, "ObjectiveMetricLookup": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Development Objective Metric Lookup", "@EntityRelationship.entityType": "sap.sf.career:ObjectiveMetricLookup", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_DevelopmentGoal"}, "type": "cds.Integer64", "key": true}, "_DevelopmentGoal": {"type": "cds.Association", "target": "DevelopmentGoal", "cardinality": {"max": 1}, "on": [{"ref": ["_DevelopmentGoal", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "id", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:ObjectiveMetricLookupId"}, "achievementNumeric": {"type": "cds.Decimal", "@EndUserText.label": "achievementNumeric", "precision": 34, "scale": 1}, "achievementText": {"type": "cds.String", "@EndUserText.label": "achievementText"}, "code": {"type": "cds.String", "@EndUserText.label": "code"}, "description": {"type": "cds.String", "@EndUserText.label": "description"}, "lastModified": {"type": "cds.String", "@EndUserText.label": "lastModified"}, "objectiveId": {"type": "cds.Integer64", "@EndUserText.label": "objectiveId"}, "index": {"type": "cds.Integer64", "@EndUserText.label": "index"}, "masterId": {"type": "cds.Integer64", "@EndUserText.label": "masterId"}, "rating": {"type": "cds.Decimal", "@EndUserText.label": "rating", "precision": 34, "scale": 1}}}, "ObjectiveCompetency": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Development Objective Competency", "@EntityRelationship.entityType": "sap.sf.career:ObjectiveCompetency", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_DevelopmentGoal"}, "type": "cds.Integer64", "key": true}, "_DevelopmentGoal": {"type": "cds.Association", "target": "DevelopmentGoal", "cardinality": {"max": 1}, "on": [{"ref": ["_DevelopmentGoal", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "competencyId", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:ObjectiveCompetencyId"}, "objectiveId": {"type": "cds.Integer64", "@EndUserText.label": "objectiveId"}, "type": {"type": "cds.String", "@EndUserText.label": "type"}}}, "ObjectiveRelatedLearningItem": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Development Objective Related Learning Items", "@EntityRelationship.entityType": "sap.sf.career:ObjectiveRelatedLearningItem", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_DevelopmentGoal"}, "type": "cds.Integer64", "key": true}, "_DevelopmentGoal": {"type": "cds.Association", "target": "DevelopmentGoal", "cardinality": {"max": 1}, "on": [{"ref": ["_DevelopmentGoal", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.String", "@EndUserText.label": "lmsGuid", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:ObjectiveRelatedLearningItemId"}, "type": {"type": "cds.String", "@EndUserText.label": "type"}}}}}