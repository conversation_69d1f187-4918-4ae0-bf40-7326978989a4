{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/succession/v1/metadata/nomineeStatus", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "Nominee<PERSON><PERSON><PERSON>", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.career", "document": {"version": "1.0.0"}}, "definitions": {"NomineeStatus": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "nominee status enum metadata", "@EntityRelationship.entityType": "sap.sf.career:<PERSON><PERSON>eStatus", "@EntityRelationship.entityIds": [{"name": "Nominee<PERSON><PERSON><PERSON>", "propertyTypes": ["sap.sf.career:NomineeStatusID"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Nominee Status Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:NomineeStatusID"}, "messageKey": {"type": "cds.String", "@EndUserText.label": "Nominee Status Message Key"}, "changeType": {"type": "cds.String", "@EndUserText.label": "Nominee Status Change Type"}}}}}