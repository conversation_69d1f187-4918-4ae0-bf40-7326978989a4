{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/succession/v1/metadata/rolePersonNomination", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "RolePersonNomination", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.career", "document": {"version": "1.0.0"}}, "definitions": {"RolePersonNomination": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "role person metadata", "@EntityRelationship.entityType": "sap.sf.career:RolePersonNomination", "@EntityRelationship.entityIds": [{"name": "RolePersonNomination", "propertyTypes": ["sap.sf.career:RolePersonNominationID"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Role Person Nomination Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:RolePersonNominationID"}, "nominationTypeId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.career:NominationType", "referencedPropertyType": "sap.sf.career:NominationTypeID"}], "type": "cds.Integer64", "@EndUserText.label": "Nomination Type Id"}, "incumbent": {"type": "cds.String", "@EndUserText.label": "Incumbent"}, "jobCode": {"type": "cds.String", "@EndUserText.label": "Job Code"}, "nominees": {"type": "cds.Association", "target": "<PERSON><PERSON><PERSON>", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Nominee Ids", "on": [{"ref": ["nominees", "nominationId"]}, "=", {"ref": ["id"]}]}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created At"}, "createdBy": {"type": "cds.String", "@EndUserText.label": "Created By"}}}}}