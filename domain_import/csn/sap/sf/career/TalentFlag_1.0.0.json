{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/career/succession/v1/metadata/talentFlag", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "TalentFlag", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.career", "document": {"version": "1.0.0"}}, "definitions": {"TalentFlag": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "TalentFlagDTO metadata", "@EntityRelationship.entityType": "sap.sf.career:TalentFlag", "@EntityRelationship.entityIds": [{"name": "TalentFlag", "propertyTypes": ["sap.sf.career:TalentFlagID"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Talent Flag Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:TalentFlagID"}, "usersSysId": {"type": "cds.String", "@EndUserText.label": "Users Sys Id"}, "riskOfLoss": {"type": "cds.String", "@EndUserText.label": "Risk of Loss"}, "impactOfLoss": {"type": "cds.String", "@EndUserText.label": "Impact of Loss"}, "reasonForLeaving": {"type": "cds.String", "@EndUserText.label": "Reason for Leaving"}, "futureLeader": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Future Leader"}, "benchStrength": {"type": "cds.String", "@EndUserText.label": "Bench Strength"}, "newToPosition": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "New to Position"}, "keyPosition": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Key Position"}, "businessSegment": {"type": "cds.String", "@EndUserText.label": "Business Segment"}, "talentPool": {"type": "cds.String", "@EndUserText.label": "Talent Pool"}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Modified At"}}}}}