{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/development/v1/metadata/template", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "DevelopmentGoalTemplate", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.career", "document": {"version": "1.0.0"}}, "definitions": {"DevelopmentGoalTemplate": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Development Objective Template", "@ODM.entityName": "CareerDevelopmentGoalTemplate", "@ODM.oid": "id", "@EntityRelationship.entityType": "sap.sf.career:DevelopmentGoalTemplate", "@EntityRelationship.entityIds": [{"name": "DevelopmentGoalTemplate", "propertyTypes": ["sap.sf.career:DevelopmentGoalTemplateID"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "DevelopmentGoal Template ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:DevelopmentGoalTemplateID", "@ObjectModel.text.association": {"=": "texts"}}, "name": {"type": "cds.String", "@EndUserText.label": "Name"}, "objectivePlanDesc": {"type": "cds.String", "@EndUserText.label": "Template Description"}, "objectivePlanLastModified": {"type": "cds.String", "@EndUserText.label": "Last Modified"}, "status": {"type": "cds.Integer64", "@EndUserText.label": "Status"}, "type": {"type": "cds.Integer64", "@EndUserText.label": "Type"}, "parentPlanId": {"type": "cds.Integer64", "@EndUserText.label": "Parent Template ID"}, "displayOrder": {"type": "cds.Integer64", "@EndUserText.label": "Display Order"}, "startDate": {"type": "cds.DateTime", "@EndUserText.label": "Template Start Date"}, "dueDate": {"type": "cds.DateTime", "@EndUserText.label": "Template Due Date"}, "defaultPlanState": {"type": "cds.String", "@EndUserText.label": "Default Template State"}, "objectivePlanVersion": {"type": "cds.String", "@EndUserText.label": "Template Version"}, "objectivePlanBackupCopyId": {"type": "cds.Integer64", "@EndUserText.label": "Template Backup Copy ID"}, "createdBy": {"type": "cds.String", "@EndUserText.label": "Created By"}, "lastModifiedBy": {"type": "cds.String", "@EndUserText.label": "Last Modified By"}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created At"}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Modified At"}, "texts": {"type": "cds.Composition", "target": "TemplateText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_id"]}, "=", {"ref": ["id"]}]}, "category": {"type": "cds.Composition", "target": "TemplateCategory", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Category", "on": [{"ref": ["category", "_id"]}, "=", {"ref": ["id"]}]}, "state": {"type": "cds.Composition", "target": "TemplateState", "cardinality": {"max": 1}, "@EndUserText.label": "State", "on": [{"ref": ["state", "_id"]}, "=", {"ref": ["id"]}]}, "createdDateTime": {"type": "cds.DateTime", "@EndUserText.label": "Created At"}, "lastModifiedTime": {"type": "cds.DateTime", "@EndUserText.label": "Modified At"}}}, "TemplateText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Development Objective Template Localized Text Entity", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.career:TemplateText", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_DevelopmentGoalTemplate"}, "type": "cds.Integer64", "key": true}, "_DevelopmentGoalTemplate": {"type": "cds.Association", "target": "DevelopmentGoalTemplate", "cardinality": {"max": 1}, "on": [{"ref": ["_DevelopmentGoalTemplate", "id"]}, "=", {"ref": ["_id"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:TemplateTextLocale", "@Semantics.language": true}, "isDefault": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "isDefault"}, "name": {"type": "cds.String", "@EndUserText.label": "name", "@Semantics.text": true}}}, "TemplateCategory": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Development Objective Template Category", "@EntityRelationship.entityType": "sap.sf.career:TemplateCategory", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_DevelopmentGoalTemplate"}, "type": "cds.Integer64", "key": true}, "_DevelopmentGoalTemplate": {"type": "cds.Association", "target": "DevelopmentGoalTemplate", "cardinality": {"max": 1}, "on": [{"ref": ["_DevelopmentGoalTemplate", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.String", "@EndUserText.label": "ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:TemplateCategoryId"}, "templateId": {"type": "cds.Integer64", "@EndUserText.label": "Template ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:TemplateCategoryTemplateId"}, "name": {"type": "cds.String", "@EndUserText.label": "Category name"}, "texts": {"type": "cds.Composition", "target": "TemplateCategoryText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_id"]}, "=", {"ref": ["id"]}, "and", {"ref": ["texts", "_templateId"]}, "=", {"ref": ["templateId"]}]}}}, "TemplateState": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Development Objective Template State", "@EntityRelationship.entityType": "sap.sf.career:TemplateState", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_DevelopmentGoalTemplate"}, "type": "cds.Integer64", "key": true}, "_DevelopmentGoalTemplate": {"type": "cds.Association", "target": "DevelopmentGoalTemplate", "cardinality": {"max": 1}, "on": [{"ref": ["_DevelopmentGoalTemplate", "id"]}, "=", {"ref": ["_id"]}]}, "label": {"type": "cds.String", "@EndUserText.label": "Label", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:TemplateStateLabel"}, "templateId": {"type": "cds.Integer64", "@EndUserText.label": "Template ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:TemplateStateTemplateId"}, "values": {"type": "cds.Composition", "target": "TemplateStateEnumValue", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Status value", "on": [{"ref": ["values", "_label"]}, "=", {"ref": ["label"]}, "and", {"ref": ["values", "_templateId"]}, "=", {"ref": ["templateId"]}]}}}, "TemplateCategoryText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Development Objective Template Texts", "@EntityRelationship.entityType": "sap.sf.career:TemplateCategoryText", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_TemplateCategory"}, "type": "cds.Integer64"}, "_TemplateCategory": {"type": "cds.Association", "target": "TemplateCategory", "cardinality": {"max": 1}, "on": [{"ref": ["_TemplateCategory", "templateId"]}, "=", {"ref": ["_templateId"]}]}, "_templateId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_TemplateCategory"}, "type": "cds.Integer64", "key": true}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:TemplateCategoryTextLocale"}, "isDefault": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "isDefault"}, "name": {"type": "cds.String", "@EndUserText.label": "name"}}}, "TemplateStateEnumValue": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Development Objective Template State Enum Value", "@EntityRelationship.entityType": "sap.sf.career:TemplateStateEnumValue", "elements": {"_label": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_TemplateState"}, "type": "cds.Integer64"}, "_TemplateState": {"type": "cds.Association", "target": "TemplateState", "cardinality": {"max": 1}, "on": [{"ref": ["_TemplateState", "templateId"]}, "=", {"ref": ["_templateId"]}]}, "_templateId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_TemplateState"}, "type": "cds.Integer64", "key": true}, "value": {"type": "cds.String", "@EndUserText.label": "Value", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:TemplateStateEnumValueValue"}, "templateId": {"type": "cds.Integer64", "@EndUserText.label": "Template ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:TemplateStateEnumValueTemplateId"}, "label": {"type": "cds.String", "@EndUserText.label": "State label"}, "completionTrigger": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Completion Trigger"}, "texts": {"type": "cds.Composition", "target": "TemplateStateEnumValueText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_templateId"]}, "=", {"ref": ["templateId"]}, "and", {"ref": ["texts", "_value"]}, "=", {"ref": ["value"]}]}}}, "TemplateStateEnumValueText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Development Objective Template State Enum Value Texts", "@EntityRelationship.entityType": "sap.sf.career:TemplateStateEnumValueText", "elements": {"_templateId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_TemplateStateEnumValue"}, "type": "cds.Integer64"}, "_TemplateStateEnumValue": {"type": "cds.Association", "target": "TemplateStateEnumValue", "cardinality": {"max": 1}, "on": [{"ref": ["_TemplateStateEnumValue", "value"]}, "=", {"ref": ["_value"]}]}, "_value": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_TemplateStateEnumValue"}, "type": "cds.Integer64", "key": true}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:TemplateStateEnumValueTextLocale"}, "isDefault": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "isDefault"}, "label": {"type": "cds.String", "@EndUserText.label": "label"}}}}}