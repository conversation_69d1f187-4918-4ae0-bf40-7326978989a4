{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/succession/v1/metadata/readiness", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "NomineeReadiness", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.career", "document": {"version": "1.0.0"}}, "definitions": {"NomineeReadiness": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "readiness metadata", "@EntityRelationship.entityType": "sap.sf.career:NomineeReadiness", "@EntityRelationship.entityIds": [{"name": "NomineeReadiness", "propertyTypes": ["sap.sf.career:NomineeReadinessID"]}], "elements": {"id": {"type": "cds.String", "@EndUserText.label": "Readiness Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:NomineeReadinessID", "@ObjectModel.text.association": {"=": "name"}}, "name": {"type": "cds.String", "@EndUserText.label": "Name"}, "index": {"type": "cds.Integer64", "@EndUserText.label": "Index"}, "texts": {"type": "cds.Composition", "target": "ReadinessLocalizedText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Readiness Localized Texts", "on": [{"ref": ["texts", "_id"]}, "=", {"ref": ["id"]}]}}}, "ReadinessLocalizedText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "ReadinessLocalizedText", "@EntityRelationship.entityType": "sap.sf.career:ReadinessLocalizedText", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_NomineeReadiness"}, "type": "cds.String", "key": true}, "_NomineeReadiness": {"type": "cds.Association", "target": "NomineeReadiness", "cardinality": {"max": 1}, "on": [{"ref": ["_NomineeReadiness", "id"]}, "=", {"ref": ["_id"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:ReadinessLocalizedTextLocale"}, "name": {"type": "cds.String", "@EndUserText.label": "Name"}}}}}