{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/career/succession/v1/metadata/talentPool", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "TalentPool", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.career", "document": {"version": "1.0.0"}}, "definitions": {"TalentPool": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Talent Pool", "@EntityRelationship.entityType": "sap.sf.career:TalentPool", "@EntityRelationship.temporalIds": [{"name": "TalentPool", "propertyTypes": ["sap.sf.career:TalentPoolId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Internal ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.career:TalentPoolId"}, "code": {"type": "cds.String", "@EndUserText.label": "Code", "length": 128}, "timeslices": {"type": "cds.Composition", "target": "TalentPoolTimeSlice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Timeslices", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "TalentPoolTimeSlice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "TalentPoolTimeSlice", "@EntityRelationship.entityType": "sap.sf.career:TalentPoolTimeSlice", "@EntityRelationship.temporalIds": [{"name": "TalentPoolTimeSlice", "propertyTypes": ["sap.sf.career:TalentPoolTimeSliceRecordId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_TalentPool"}, "type": "cds.Integer64", "key": true}, "_TalentPool": {"type": "cds.Association", "target": "TalentPool", "cardinality": {"max": 1}, "on": [{"ref": ["_TalentPool", "id"]}, "=", {"ref": ["_id"]}]}, "effectiveStatus": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "validFrom": {"type": "cds.Date", "@EndUserText.label": "Start Date"}, "validTo": {"type": "cds.Date", "@EndUserText.label": "End Date"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 255}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 255}, "owner": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Owner", "length": 100}, "type": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueId"}, {"referencedEntityType": "sap.sf.extensibility:TalentPoolTypePickList", "referencedPropertyType": "sap.sf.extensibility:TalentPoolTypePickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Type"}, "enableReadiness": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Enable Readiness"}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.career:TalentPoolTimeSliceRecordId"}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdDate": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "lastModifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "lastModifiedDate": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "proxyUser": {"type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "texts": {"type": "cds.Composition", "target": "TalentPoolText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_recordId"]}, "=", {"ref": ["recordId"]}]}}}, "TalentPoolText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized value for translatable data type field", "@EntityRelationship.entityType": "sap.sf.career:TalentPoolText", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_TalentPoolTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_TalentPoolTimeSlice": {"type": "cds.Association", "target": "TalentPoolTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_TalentPoolTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "length": 128, "key": true, "@EntityRelationship.propertyType": "sap.sf.career:TalentPoolTextLocale"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 255}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 255}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "default"}}}}}