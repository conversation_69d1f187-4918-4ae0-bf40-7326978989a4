{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/ecosystem/wholeself/v1/metadata/libraryType", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "LibraryType", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.capabilities", "document": {"version": "1.0.0"}}, "definitions": {"LibraryType": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "TIH LibraryType", "@ODM.entityName": "WorkforceCapabilityTypeCode", "@ODM.oid": "uuid", "@EntityRelationship.entityType": "sap.sf.capabilities:LibraryType", "@EntityRelationship.entityIds": [{"name": "LibraryType", "propertyTypes": ["sap.sf.capabilities:LibraryTypeId"]}], "elements": {"createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created At"}, "createdBy": {"type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "lastModifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified At"}, "lastModifiedBy": {"type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "libraryTypeId": {"type": "cds.Integer64", "@EndUserText.label": "Library Type Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.capabilities:LibraryTypeId", "@ObjectModel.text.association": {"=": "libraryTypeTexts"}}, "externalId": {"type": "cds.String", "@EndUserText.label": "External Id", "length": 256}, "uuid": {"type": "cds.String", "@EndUserText.label": "UUID", "length": 256}, "code": {"type": "cds.String", "@EndUserText.label": "Code", "length": 256}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 50}, "defaultLocale": {"type": "cds.String", "@EndUserText.label": "Default Locale", "length": 256}, "name": {"type": "cds.String", "@EndUserText.label": "Library Type Name", "length": 256}, "nameInPlural": {"type": "cds.String", "@EndUserText.label": "Library Type Name In Plural", "length": 256}, "proficiencySetId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.capabilities:ProficiencySet", "referencedPropertyType": "sap.sf.workforce:ProficiencySetId"}], "type": "cds.Integer64", "@EndUserText.label": "Proficiency Set Id"}, "libraryTypeTexts": {"type": "cds.Composition", "target": "LibraryTypeText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "LibraryTypeTexts", "on": [{"ref": ["libraryTypeTexts", "_libraryTypeId"]}, "=", {"ref": ["libraryTypeId"]}]}}}, "LibraryTypeText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Library Type Text", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.capabilities:LibraryTypeText", "elements": {"_libraryTypeId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_LibraryType"}, "type": "cds.Integer64", "key": true}, "_LibraryType": {"type": "cds.Association", "target": "LibraryType", "cardinality": {"max": 1}, "on": [{"ref": ["_LibraryType", "libraryTypeId"]}, "=", {"ref": ["_libraryTypeId"]}]}, "description": {"type": "cds.String", "@EndUserText.label": "Library Type Description", "length": 4000, "@Semantics.text": true}, "libraryTypeTextId": {"type": "cds.Integer64", "@EndUserText.label": "Library Type Text Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.capabilities:LibraryTypeTextLibraryTypeTextId"}, "name": {"type": "cds.String", "@EndUserText.label": "Library Type Name", "length": 256, "@Semantics.text": true}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "length": 256, "key": true, "@Semantics.language": true}}}}}