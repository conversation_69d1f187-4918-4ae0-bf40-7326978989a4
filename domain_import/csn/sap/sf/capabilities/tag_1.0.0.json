{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/ecosystem/wholeself/v1/metadata/tag", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "Tag", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.capabilities", "document": {"version": "1.0.0"}}, "definitions": {"Tag": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "TIH Tag", "@ODM.entityName": "WorkforceCapabilityCatalog", "@EntityRelationship.entityType": "sap.sf.capabilities:Tag", "@EntityRelationship.entityIds": [{"name": "Tag", "propertyTypes": ["sap.sf.capabilities:TagId"]}], "elements": {"tagId": {"type": "cds.Integer64", "@EndUserText.label": "Tag Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.capabilities:TagId", "@ObjectModel.text.association": {"=": "tagTexts"}}, "tagLabel": {"type": "cds.String", "@EndUserText.label": "Tag Label", "length": 256}, "tagValue": {"type": "cds.String", "@EndUserText.label": "Tag Value", "length": 256}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 50}, "tagType": {"type": "cds.String", "@EndUserText.label": "Tag Type", "length": 50}, "tagTexts": {"type": "cds.Composition", "target": "TagText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "TagTexts", "on": [{"ref": ["tagTexts", "_tagId"]}, "=", {"ref": ["tagId"]}]}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created At"}, "createdBy": {"type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "lastModifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified At"}, "lastModifiedBy": {"type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}}}, "TagText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Tag Text", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.capabilities:TagText", "elements": {"_tagId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Tag"}, "type": "cds.Integer64", "key": true}, "_Tag": {"type": "cds.Association", "target": "Tag", "cardinality": {"max": 1}, "on": [{"ref": ["_Tag", "tagId"]}, "=", {"ref": ["_tagId"]}]}, "tagTextId": {"type": "cds.Integer64", "@EndUserText.label": "Tag Text Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.capabilities:TagTextTagTextId"}, "tagLabel": {"type": "cds.String", "@EndUserText.label": "Tag Label", "length": 256, "@Semantics.text": true}, "tagValue": {"type": "cds.String", "@EndUserText.label": "Tag Value", "length": 256, "@Semantics.text": true}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "length": 256, "key": true, "@Semantics.language": true}}}}}