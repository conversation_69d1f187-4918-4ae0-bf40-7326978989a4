{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/ecosystem/wholeself/v1/metadata/library", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "Library", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.capabilities", "document": {"version": "1.0.0"}}, "definitions": {"Library": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "TIH Library", "@ODM.entityName": "WorkforceCapability", "@ODM.oid": "uuid", "@EntityRelationship.entityType": "sap.sf.capabilities:Library", "@EntityRelationship.entityIds": [{"name": "Library", "propertyTypes": ["sap.sf.capabilities:LibraryId"]}], "elements": {"libraryId": {"type": "cds.Integer64", "@EndUserText.label": "Library Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.capabilities:LibraryId", "@ObjectModel.text.association": {"=": "libraryTexts"}}, "name": {"type": "cds.String", "@EndUserText.label": "Library Name", "length": 256}, "description": {"type": "cds.String", "@EndUserText.label": "Library Description", "length": 4000}, "addedType": {"type": "cds.String", "@EndUserText.label": "Added Type", "length": 50}, "defaultLocale": {"type": "cds.String", "@EndUserText.label": "Default Locale", "length": 256}, "externalId": {"type": "cds.String", "@EndUserText.label": "External Id", "length": 256}, "uuid": {"type": "cds.String", "@EndUserText.label": "UUID", "length": 256}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created At"}, "createdBy": {"type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "lastModifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified At"}, "lastModifiedBy": {"type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 50}, "sources": {"type": "cds.Composition", "target": "Source", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Sources", "on": [{"ref": ["sources", "_libraryId"]}, "=", {"ref": ["libraryId"]}]}, "libraryType": {"type": "cds.Composition", "target": "LibraryTypeMap", "cardinality": {"max": 1}, "@EndUserText.label": "LibraryType", "on": [{"ref": ["libraryType", "_libraryId"]}, "=", {"ref": ["libraryId"]}]}, "libraryTexts": {"type": "cds.Composition", "target": "LibraryText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "LibraryTexts", "on": [{"ref": ["libraryTexts", "_libraryId"]}, "=", {"ref": ["libraryId"]}]}, "proficiencySet": {"type": "cds.Composition", "target": "LibraryProficiencySetMap", "cardinality": {"max": 1}, "@EndUserText.label": "ProficiencySet", "on": [{"ref": ["proficiencySet", "_libraryId"]}, "=", {"ref": ["libraryId"]}]}, "tags": {"type": "cds.Association", "target": "LibraryTagMap", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Tags", "on": [{"ref": ["tags", "libraryId"]}, "=", {"ref": ["libraryId"]}]}}}, "Source": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Source", "@EntityRelationship.entityType": "sap.sf.capabilities:Source", "elements": {"_libraryId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Library"}, "type": "cds.Integer64", "key": true}, "_Library": {"type": "cds.Association", "target": "Library", "cardinality": {"max": 1}, "on": [{"ref": ["_Library", "libraryId"]}, "=", {"ref": ["_libraryId"]}]}, "sourceId": {"type": "cds.Integer64", "@EndUserText.label": "Source Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.capabilities:SourceSourceId"}, "sourceType": {"type": "cds.String", "@EndUserText.label": "Source Type", "length": 50}}}, "LibraryTypeMap": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Library Type", "@EntityRelationship.entityType": "sap.sf.capabilities:LibraryTypeMap", "elements": {"_libraryId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Library"}, "type": "cds.Integer64", "key": true}, "_Library": {"type": "cds.Association", "target": "Library", "cardinality": {"max": 1}, "on": [{"ref": ["_Library", "libraryId"]}, "=", {"ref": ["_libraryId"]}]}, "libraryTypeId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.capabilities:LibraryType", "referencedPropertyType": "sap.sf.capabilities:LibraryTypeId"}], "type": "cds.Integer64", "@EndUserText.label": "Library Type Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.capabilities:LibraryTypeMapLibraryTypeId"}, "code": {"type": "cds.String", "@EndUserText.label": "Library Type Code", "length": 256}}}, "LibraryText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Library Text", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.capabilities:LibraryText", "elements": {"_libraryId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Library"}, "type": "cds.Integer64", "key": true}, "_Library": {"type": "cds.Association", "target": "Library", "cardinality": {"max": 1}, "on": [{"ref": ["_Library", "libraryId"]}, "=", {"ref": ["_libraryId"]}]}, "libraryTextId": {"type": "cds.Integer64", "@EndUserText.label": "Library Text Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.capabilities:LibraryTextLibraryTextId"}, "name": {"type": "cds.String", "@EndUserText.label": "Library Name", "length": 256, "@Semantics.text": true}, "description": {"type": "cds.String", "@EndUserText.label": "Library Description", "length": 4000, "@Semantics.text": true}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "length": 256, "key": true, "@Semantics.language": true}}}, "LibraryProficiencySetMap": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Proficiency Set", "@EntityRelationship.entityType": "sap.sf.capabilities:LibraryProficiencySetMap", "elements": {"_libraryId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Library"}, "type": "cds.Integer64", "key": true}, "_Library": {"type": "cds.Association", "target": "Library", "cardinality": {"max": 1}, "on": [{"ref": ["_Library", "libraryId"]}, "=", {"ref": ["_libraryId"]}]}, "proficiencySetId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.capabilities:ProficiencySet", "referencedPropertyType": "sap.sf.capabilities:ProficiencySetId"}], "type": "cds.Integer64", "@EndUserText.label": "Proficiency set Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.capabilities:LibraryProficiencySetMapProficiencySetId"}}}, "LibraryTagMap": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "LibraryTagMap", "@EntityRelationship.entityType": "sap.sf.capabilities:LibraryTagMap", "elements": {"libraryId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Library"}, "type": "cds.Integer64", "key": true}, "_Library": {"type": "cds.Association", "target": "Library", "cardinality": {"max": 1}, "on": [{"ref": ["_Library", "libraryId"]}, "=", {"ref": ["libraryId"]}]}, "tagId": {"type": "cds.String", "key": true, "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.capabilities:tag", "referencedPropertyType": "sap.sf.capabilities:tagTagId"}], "@EndUserText.label": "TagId"}}}}}