{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/ecosystem/wholeself/v1/metadata/proficiencySet", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "ProficiencySet", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.capabilities", "document": {"version": "1.0.0"}}, "definitions": {"ProficiencySet": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "TIH ProficiencySet", "@ODM.entityName": "WorkforceCapabilityProficiencyScale", "@EntityRelationship.entityType": "sap.sf.capabilities:ProficiencySet", "@EntityRelationship.entityIds": [{"name": "ProficiencySet", "propertyTypes": ["sap.sf.capabilities:ProficiencySetId"]}], "elements": {"createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created At"}, "createdBy": {"type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "lastModifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified At"}, "lastModifiedBy": {"type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "proficiencySetId": {"type": "cds.Integer64", "@EndUserText.label": "Proficiency Set Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.capabilities:ProficiencySetId", "@ObjectModel.text.association": {"=": "proficiencySetTexts"}}, "proficiencySetName": {"type": "cds.String", "@EndUserText.label": "Proficiency Set Name", "length": 256}, "description": {"type": "cds.String", "@EndUserText.label": "Proficiency Set Description", "length": 4000}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 50}, "proficiencySetTexts": {"type": "cds.Composition", "target": "ProficiencySetText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "ProficiencySetTexts", "on": [{"ref": ["proficiencySetTexts", "_proficiencySetId"]}, "=", {"ref": ["proficiencySetId"]}]}, "proficiencyLevels": {"type": "cds.Composition", "target": "ProficiencyLevel", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "ProficiencyLevels", "on": [{"ref": ["proficiencyLevels", "_proficiencySetId"]}, "=", {"ref": ["proficiencySetId"]}]}}}, "ProficiencySetText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Proficiency Set Text", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.capabilities:ProficiencySetText", "elements": {"_proficiencySetId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_ProficiencySet"}, "type": "cds.Integer64", "key": true}, "_ProficiencySet": {"type": "cds.Association", "target": "ProficiencySet", "cardinality": {"max": 1}, "on": [{"ref": ["_ProficiencySet", "proficiencySetId"]}, "=", {"ref": ["_proficiencySetId"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "length": 256, "key": true, "@Semantics.language": true}, "name": {"type": "cds.String", "@EndUserText.label": "Proficiency Set Name", "length": 256, "@Semantics.text": true}, "description": {"type": "cds.String", "@EndUserText.label": "Proficiency Set Description", "length": 4000, "@Semantics.text": true}, "proficiencySetTextId": {"type": "cds.Integer64", "@EndUserText.label": "Proficiency Set Text Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.capabilities:ProficiencySetTextProficiencySetTextId"}}}, "ProficiencyLevel": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Proficiency Level", "@EntityRelationship.entityType": "sap.sf.capabilities:ProficiencyLevel", "elements": {"_proficiencySetId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_ProficiencySet"}, "type": "cds.Integer64", "key": true}, "_ProficiencySet": {"type": "cds.Association", "target": "ProficiencySet", "cardinality": {"max": 1}, "on": [{"ref": ["_ProficiencySet", "proficiencySetId"]}, "=", {"ref": ["_proficiencySetId"]}]}, "proficiencyLevelId": {"type": "cds.Integer64", "@EndUserText.label": "Proficiency Level Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.capabilities:ProficiencyLevelProficiencyLevelId", "@ObjectModel.text.association": {"=": "proficiencyLevelTexts"}}, "levelValue": {"type": "cds.Integer64", "@EndUserText.label": "Proficiency Level Value"}, "levelLabel": {"type": "cds.String", "@EndUserText.label": "Proficiency Level Label", "length": 256}, "description": {"type": "cds.String", "@EndUserText.label": "Proficiency Level Description", "length": 4000}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 50}, "proficiencyLevelTexts": {"type": "cds.Composition", "target": "ProficiencyLevelText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "ProficiencyLevelTexts", "on": [{"ref": ["proficiencyLevelTexts", "_proficiencyLevelId"]}, "=", {"ref": ["proficiencyLevelId"]}]}}}, "ProficiencyLevelText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "ProficiencyLevelText", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.capabilities:ProficiencyLevelText", "elements": {"_proficiencyLevelId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_ProficiencyLevel"}, "type": "cds.Integer64", "key": true}, "_ProficiencyLevel": {"type": "cds.Association", "target": "ProficiencyLevel", "cardinality": {"max": 1}, "on": [{"ref": ["_ProficiencyLevel", "proficiencyLevelId"]}, "=", {"ref": ["_proficiencyLevelId"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "length": 256, "key": true, "@Semantics.language": true}, "levelLabel": {"type": "cds.String", "@EndUserText.label": "Level Label", "length": 256, "@Semantics.text": true}, "description": {"type": "cds.String", "@EndUserText.label": "Proficiency Level Description", "length": 4000, "@Semantics.text": true}, "proficiencyLevelTextId": {"type": "cds.Integer64", "@EndUserText.label": "Proficiency Level Text Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.capabilities:ProficiencyLevelTextProficiencyLevelTextId"}}}}}