{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/ecosystem/wholeself/v1/metadata/growthPortfolioAttribute", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "GrowthPortfolioAttribute", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.capabilities", "document": {"version": "1.0.0"}}, "definitions": {"GrowthPortfolioAttribute": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Growth Portfolio", "@ODM.entityName": "WorkforceCapabilityAssignment", "@EntityRelationship.entityType": "sap.sf.capabilities:GrowthPortfolioAttribute", "@EntityRelationship.entityIds": [{"name": "GrowthPortfolioAttribute", "propertyTypes": ["sap.sf.capabilities:GrowthPortfolioId"]}], "elements": {"growthPortfolioId": {"type": "cds.Integer64", "@EndUserText.label": "Growth Portfolio Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.capabilities:GrowthPortfolioId"}, "personId": {"type": "cds.String", "@EndUserText.label": "Person Id", "length": 32}, "attributeId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.capabilities:Library", "referencedPropertyType": "sap.sf.workforce:LibraryId"}], "type": "cds.Integer64", "@EndUserText.label": "Attribute Id"}, "attributeType": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.capabilities:LibraryType", "referencedPropertyType": "sap.sf.workforce:LibraryTypeId"}], "type": "cds.Integer64", "@EndUserText.label": "Attribute Type Id"}, "sourceType": {"type": "cds.String", "@EndUserText.label": "Source Type", "length": 50}, "passionateAbout": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Passionate About"}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 50}, "expectedProficiencyLevel": {"type": "cds.Integer64", "@EndUserText.label": "Expected Proficiency Level"}, "isRoleBased": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Role Based"}, "proficiencyLevel": {"type": "cds.Integer64", "@EndUserText.label": "Proficiency Level"}, "assessedDate": {"type": "cds.DateTime", "@EndUserText.label": "Assessed Date"}, "createdBy": {"type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "lastModifiedBy": {"type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created At"}, "lastModifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified At"}, "proxyBy": {"type": "cds.String", "@EndUserText.label": "Proxy By", "length": 100}, "assessmentHistory": {"type": "cds.Composition", "target": "AssessmentHistory", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "AssessmentHistory", "on": [{"ref": ["assessmentHistory", "_growthPortfolioId"]}, "=", {"ref": ["growthPortfolioId"]}]}}}, "AssessmentHistory": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Assessment History", "@EntityRelationship.entityType": "sap.sf.capabilities:AssessmentHistory", "elements": {"_growthPortfolioId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_GrowthPortfolioAttribute"}, "type": "cds.Integer64", "key": true}, "_GrowthPortfolioAttribute": {"type": "cds.Association", "target": "GrowthPortfolioAttribute", "cardinality": {"max": 1}, "on": [{"ref": ["_GrowthPortfolioAttribute", "growthPortfolioId"]}, "=", {"ref": ["_growthPortfolioId"]}]}, "growthPortfolioId": {"type": "cds.Integer64", "@EndUserText.label": "Growth Portfolio Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.capabilities:AssessmentHistoryGrowthPortfolioId"}, "sourceType": {"type": "cds.String", "@EndUserText.label": "Source Type", "length": 50}, "passionateAbout": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Passionate About"}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 50}, "expectedProficiencyLevel": {"type": "cds.Integer64", "@EndUserText.label": "Expected Proficiency Level"}, "isRoleBased": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Role Based"}, "proficiencyLevel": {"type": "cds.Integer64", "@EndUserText.label": "Proficiency Level"}, "assessedDate": {"type": "cds.DateTime", "@EndUserText.label": "Assessed Date"}, "createdBy": {"type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "lastModifiedBy": {"type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created At"}, "lastModifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified At"}, "proxyBy": {"type": "cds.String", "@EndUserText.label": "Proxy By", "length": 100}}}}}