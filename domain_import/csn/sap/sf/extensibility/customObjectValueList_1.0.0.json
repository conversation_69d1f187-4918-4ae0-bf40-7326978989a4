{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/extensibility/custom/v1/metadata/customObjectValueList", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "CustomObjectValueList", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.extensibility", "document": {"version": "1.0.0"}}, "definitions": {"CustomObjectValueList": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Generic Custom Object Value List", "@EntityRelationship.entityType": "sap.sf.extensibility:CustomObjectValueList", "@EntityRelationship.temporalIds": [{"name": "CustomObjectValueList", "propertyTypes": ["sap.sf.extensibility:CustomObjectValueListId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.extensibility:CustomObjectValueListId"}, "externalCode": {"type": "cds.String", "@EndUserText.label": "externalCode", "length": 128}, "objectType": {"type": "cds.String", "@EndUserText.label": "Object type", "length": 128}, "timeslices": {"type": "cds.Composition", "target": "CustomObjectValueListTimeSlice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Timeslices", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "CustomObjectValueListTimeSlice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "CustomObjectValueListTimeSlice", "@EntityRelationship.entityType": "sap.sf.extensibility:CustomObjectValueListTimeSlice", "@EntityRelationship.temporalIds": [{"name": "CustomObjectValueListTimeSlice", "propertyTypes": ["sap.sf.extensibility:CustomObjectValueListTimeSliceRecordId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_CustomObjectValueList"}, "type": "cds.Integer64", "key": true}, "_CustomObjectValueList": {"type": "cds.Association", "target": "CustomObjectValueList", "cardinality": {"max": 1}, "on": [{"ref": ["_CustomObjectValueList", "id"]}, "=", {"ref": ["_id"]}]}, "externalName": {"type": "cds.String", "@EndUserText.label": "externalName", "length": 255}, "validFrom": {"type": "cds.Date", "@EndUserText.label": "validFrom"}, "validTo": {"type": "cds.Date", "@EndUserText.label": "validTo"}, "recordId": {"type": "cds.String", "@EndUserText.label": "recordId", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.extensibility:CustomObjectValueListTimeSliceRecordId"}, "status": {"type": "cds.String", "@EndUserText.label": "status"}, "createdBy": {"type": "cds.String", "@EndUserText.label": "created<PERSON>y"}, "createdDate": {"type": "cds.DateTime", "@EndUserText.label": "createdDate"}, "lastModifiedBy": {"type": "cds.String", "@EndUserText.label": "lastModifiedBy"}, "lastModifiedDate": {"type": "cds.DateTime", "@EndUserText.label": "lastModifiedDate"}, "proxyUser": {"type": "cds.String", "@EndUserText.label": "proxyUser", "length": 255}, "texts": {"type": "cds.Composition", "target": "CustomObjectValueListTimeSliceText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_recordId"]}, "=", {"ref": ["recordId"]}]}}}, "CustomObjectValueListTimeSliceText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized value for translatable data type field", "@EntityRelationship.entityType": "sap.sf.extensibility:CustomObjectValueListTimeSliceText", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_CustomObjectValueListTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_CustomObjectValueListTimeSlice": {"type": "cds.Association", "target": "CustomObjectValueListTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_CustomObjectValueListTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "length": 128, "key": true, "@EntityRelationship.propertyType": "sap.sf.extensibility:CustomObjectValueListTimeSliceTextLocale"}, "externalName": {"type": "cds.String", "@EndUserText.label": "Localized label", "length": 255}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "default"}}}}}