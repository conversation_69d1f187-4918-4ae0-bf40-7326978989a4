{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/extensibility/general/v1/metadata/pickList", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "PickList", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.extensibility", "document": {"version": "1.0.0"}}, "definitions": {"PickList": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "PickList", "@EntityRelationship.entityType": "sap.sf.extensibility:PickList", "@EntityRelationship.temporalIds": [{"name": "PickList", "propertyTypes": ["sap.sf.extensibility:PickListId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854776000", "maximum": "9223372036854776000"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.extensibility:PickListId"}, "listId": {"type": "cds.String", "@EndUserText.label": "id", "length": 128}, "timeslices": {"type": "cds.Composition", "target": "PickListTimeSlice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Timeslices", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "PickListTimeSlice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "PickListTimeSlice", "@EntityRelationship.entityType": "sap.sf.extensibility:PickListTimeSlice", "@EntityRelationship.temporalIds": [{"name": "PickListTimeSlice", "propertyTypes": ["sap.sf.extensibility:PickListTimeSliceRecordId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PickList"}, "type": "cds.Integer64", "key": true}, "_PickList": {"type": "cds.Association", "target": "PickList", "cardinality": {"max": 1}, "on": [{"ref": ["_PickList", "id"]}, "=", {"ref": ["_id"]}]}, "validFrom": {"type": "cds.Date", "@EndUserText.label": "validFrom"}, "validTo": {"type": "cds.Date", "@EndUserText.label": "validTo"}, "name": {"type": "cds.String", "@EndUserText.label": "name", "length": 128}, "status": {"type": "cds.String", "@EndUserText.label": "status"}, "parentPickList": {"type": "cds.Integer64", "@EndUserText.label": "parentPickList"}, "recordId": {"type": "cds.String", "@EndUserText.label": "recordId", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.extensibility:PickListTimeSliceRecordId"}, "values": {"type": "cds.Composition", "target": "PickListValue", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Values", "on": [{"ref": ["values", "_recordId"]}, "=", {"ref": ["recordId"]}]}}}, "PickListValue": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "PickListValue", "@EntityRelationship.entityType": "sap.sf.extensibility:PickListValue", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PickListTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_PickListTimeSlice": {"type": "cds.Association", "target": "PickListTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_PickListTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "the immutable id of a picklist value", "@Semantics.valueRange": {"minimum": "-9223372036854776000", "maximum": "9223372036854776000"}}, "optionId": {"type": "cds.Integer64", "@EndUserText.label": "the immutable id of a picklist option", "@Semantics.valueRange": {"minimum": "-9223372036854776000", "maximum": "9223372036854776000"}}, "label": {"type": "cds.String", "@EndUserText.label": "An option label in the preferred locale"}, "externalCode": {"type": "cds.String", "@EndUserText.label": "id", "length": 128}, "status": {"type": "cds.String", "@EndUserText.label": "status"}, "recordId": {"type": "cds.String", "@EndUserText.label": "recordId", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.extensibility:PickListValueRecordId"}, "parentValueId": {"type": "cds.Integer64", "@EndUserText.label": "ParentValueId"}, "texts": {"type": "cds.Composition", "target": "PickListValueText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_recordId"]}, "=", {"ref": ["recordId"]}]}}}, "PickListValueText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized Label of Picklist value", "@EntityRelationship.entityType": "sap.sf.extensibility:PickListValueText", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PickListValue"}, "type": "cds.String", "length": 255, "key": true}, "_PickListValue": {"type": "cds.Association", "target": "PickListValue", "cardinality": {"max": 1}, "on": [{"ref": ["_PickListValue", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.extensibility:PickListValueTextLocale"}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "<PERSON><PERSON><PERSON>"}, "label": {"type": "cds.String", "@EndUserText.label": "Label"}}}}}