{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/workforce/assignment/compensation/v1/metadata/compensationInfo", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "CompensationInfo", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.workforce", "document": {"version": "1.0.0"}}, "definitions": {"CompensationInfo": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Compensation Information", "@ODM.entityName": "WorkforceCompensation", "@EntityRelationship.entityType": "sap.sf.workforce:CompensationInfo", "@EntityRelationship.temporalIds": [{"name": "CompensationInfo", "propertyTypes": ["sap.sf.workforce:CompensationInfoId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"id": {"type": "cds.String", "@EndUserText.label": "Assignment UUID", "length": 32, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:CompensationInfoId"}, "compDetails": {"type": "cds.Composition", "target": "CompDetail", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Compensation Information", "on": [{"ref": ["compDetails", "_id"]}, "=", {"ref": ["id"]}]}, "assignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:Assignment", "referencedPropertyType": "sap.sf.workforce:AssignmentId"}], "type": "cds.String", "@EndUserText.label": "Assignment UUID", "length": 32}}}, "CompDetail": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Compensation Information", "@EntityRelationship.entityType": "sap.sf.workforce:CompDetail", "@EntityRelationship.temporalIds": [{"name": "CompDetail", "propertyTypes": ["sap.sf.workforce:CompDetailId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_CompensationInfo"}, "type": "cds.String", "length": 32, "key": true}, "_CompensationInfo": {"type": "cds.Association", "target": "CompensationInfo", "cardinality": {"max": 1}, "on": [{"ref": ["_CompensationInfo", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:CompDetailId"}, "countryCode": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Country", "referencedPropertyType": "sap.sf.foundationobjects:CountryCode"}], "type": "cds.String", "@EndUserText.label": "Country Code", "length": 3}, "compensationStructure": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:CompensationStructure", "referencedPropertyType": "sap.sf.extensibility:CompensationStructureId"}], "type": "cds.Integer64", "@EndUserText.label": "Compensation Structure", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "prorationFactor": {"type": "cds.Decimal", "@EndUserText.label": "Proration Factor", "precision": 34, "scale": 3}, "payType": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:PayTypePickList", "referencedPropertyType": "sap.sf.extensibility:PayTypePickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Pay Type", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "notes": {"type": "cds.String", "@EndUserText.label": "Notes", "length": 4000}, "seqNumber": {"type": "cds.Integer64", "@EndUserText.label": "Sequence Number", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "payGroup": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:PayGroup", "referencedPropertyType": "sap.sf.foundationobjects:PayGroupId"}], "type": "cds.Integer64", "@EndUserText.label": "Pay Group", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "isEligibleForBenefits": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Eligible For Benefit"}, "isEligibleForCar": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Eligible For Car"}, "benefitsRate": {"type": "cds.Decimal", "@EndUserText.label": "Benefits Rate", "precision": 34, "scale": 2}, "isHighlyCompensatedEmployee": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Highly Compensated Employee"}, "isInsider": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Insider As Defined By Statute"}, "pensionableSalary": {"type": "cds.Decimal", "@EndUserText.label": "Pensionable Salary", "precision": 34, "scale": 3}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "startDate": {"type": "cds.Date", "@EndUserText.label": "Event Date"}, "endDate": {"type": "cds.Date", "@EndUserText.label": "End Date"}, "eventReason": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:EventReason", "referencedPropertyType": "sap.sf.foundationobjects:EventReasonId"}], "type": "cds.Integer64", "@EndUserText.label": "Event Reason", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "event": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:eventPickList", "referencedPropertyType": "sap.sf.extensibility:eventPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Event", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "lastModifiedSource": {"type": "cds.String", "@EndUserText.label": "Last Modified Source"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}, "wfRequestId": {"type": "cds.Integer64", "@EndUserText.label": "wfRequestId"}, "recurringPayComponent": {"type": "cds.Composition", "target": "RecurringPayComponent", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Recurring Pay Components", "on": [{"ref": ["recurringPayComponent", "_id"]}, "=", {"ref": ["id"]}]}, "targetPayComponent": {"type": "cds.Composition", "target": "TargetPayComponent", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Pay Targets", "on": [{"ref": ["targetPayComponent", "_id"]}, "=", {"ref": ["id"]}]}}}, "RecurringPayComponent": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Recurring Pay Components", "@EntityRelationship.entityType": "sap.sf.workforce:RecurringPayComponent", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_CompDetail"}, "type": "cds.Integer64", "key": true}, "_CompDetail": {"type": "cds.Association", "target": "CompDetail", "cardinality": {"max": 1}, "on": [{"ref": ["_CompDetail", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:RecurringPayComponentId"}, "payComponent": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:PayComponent", "referencedPropertyType": "sap.sf.foundationobjects:PayComponentId"}], "type": "cds.Integer64", "@EndUserText.label": "Pay Component", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "currencyCode": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Currency", "referencedPropertyType": "sap.sf.foundationobjects:CurrencyId"}], "type": "cds.String", "@EndUserText.label": "<PERSON><PERSON><PERSON><PERSON>", "length": 32}, "frequency": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Frequency", "referencedPropertyType": "sap.sf.foundationobjects:FrequencyId"}], "type": "cds.Integer64", "@EndUserText.label": "Frequency", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "numberOfUnits": {"type": "cds.Decimal", "@EndUserText.label": "Number", "precision": 34, "scale": 3}, "unitOfMeasure": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:UnitOfMeasure", "referencedPropertyType": "sap.sf.foundationobjects:UnitOfMeasureId"}], "type": "cds.Integer64", "@EndUserText.label": "Unit of Measure", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "startDate": {"type": "cds.Date", "@EndUserText.label": "Event Date"}, "endDate": {"type": "cds.Date", "@EndUserText.label": "End Date"}, "seqNumber": {"type": "cds.Integer64", "@EndUserText.label": "Sequence Number", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "amountValue": {"type": "cds.Decimal", "@EndUserText.label": "Amount", "precision": 34, "scale": 2}, "percentageValue": {"type": "cds.Decimal", "@EndUserText.label": "Percentage", "precision": 34, "scale": 2}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}}}, "TargetPayComponent": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Pay Targets", "@EntityRelationship.entityType": "sap.sf.workforce:TargetPayComponent", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_CompDetail"}, "type": "cds.Integer64", "key": true}, "_CompDetail": {"type": "cds.Association", "target": "CompDetail", "cardinality": {"max": 1}, "on": [{"ref": ["_CompDetail", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:TargetPayComponentId"}, "payComponent": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:PayComponent", "referencedPropertyType": "sap.sf.foundationobjects:PayComponentId"}], "type": "cds.Integer64", "@EndUserText.label": "Pay Component", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "currencyCode": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Currency", "referencedPropertyType": "sap.sf.foundationobjects:CurrencyId"}], "type": "cds.String", "@EndUserText.label": "<PERSON><PERSON><PERSON><PERSON>", "length": 32}, "frequency": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Frequency", "referencedPropertyType": "sap.sf.foundationobjects:FrequencyId"}], "type": "cds.Integer64", "@EndUserText.label": "Frequency", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "numberOfUnits": {"type": "cds.Decimal", "@EndUserText.label": "Number", "precision": 34, "scale": 3}, "unitOfMeasure": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:UnitOfMeasure", "referencedPropertyType": "sap.sf.foundationobjects:UnitOfMeasureId"}], "type": "cds.Integer64", "@EndUserText.label": "Unit of Measure", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "startDate": {"type": "cds.Date", "@EndUserText.label": "Event Date"}, "endDate": {"type": "cds.Date", "@EndUserText.label": "End Date"}, "seqNumber": {"type": "cds.Integer64", "@EndUserText.label": "Sequence Number", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "amountValue": {"type": "cds.Decimal", "@EndUserText.label": "Amount", "precision": 34, "scale": 2}, "percentageValue": {"type": "cds.Decimal", "@EndUserText.label": "Percentage", "precision": 34, "scale": 2}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}}}}}