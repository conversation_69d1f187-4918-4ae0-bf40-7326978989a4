{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/workforce/v1/metadata/workforcePersonProfile", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "WorkforcePersonProfile", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.workforce", "document": {"version": "1.0.0"}}, "definitions": {"WorkforcePersonProfile": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "The WorkforcePersonProfile", "@ODM.entityName": "WorkforcePerson", "@EntityRelationship.entityType": "sap.sf.workforce:WorkforcePersonProfile", "@EntityRelationship.entityIds": [{"name": "WorkforcePersonProfile", "propertyTypes": ["sap.sf.workforce:WorkforcePersonProfileId"]}], "elements": {"id": {"type": "cds.String", "@EndUserText.label": "Person UUID", "length": 32, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:WorkforcePersonProfileId"}, "externalId": {"type": "cds.String", "@EndUserText.label": "Person ID", "length": 100}, "displayName": {"type": "cds.String", "@EndUserText.label": "Display Name", "length": 4000}, "personId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:Person", "referencedPropertyType": "sap.sf.workforce:PersonId"}], "type": "cds.String", "@EndUserText.label": "Person UUID", "length": 32}, "workProfiles": {"type": "cds.Association", "target": "WorkProfile", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Work Profile", "on": [{"ref": ["workProfiles", "workforcePersonProfileId"]}, "=", {"ref": ["id"]}]}}}}}