{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/workforce/v1/metadata/workProfile", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "WorkProfile", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.workforce", "document": {"version": "1.0.0"}}, "definitions": {"WorkProfile": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "The schema of the WorkProfile", "@ODM.entityName": "WorkforceAssignment", "@EntityRelationship.entityType": "sap.sf.workforce:WorkProfile", "@EntityRelationship.entityIds": [{"name": "WorkProfile", "propertyTypes": ["sap.sf.workforce:WorkProfileId"]}], "elements": {"id": {"type": "cds.String", "@EndUserText.label": "Assignment UUID", "length": 32, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:WorkProfileId"}, "externalId": {"type": "cds.String", "@EndUserText.label": "Assignment ID", "length": 100}, "isActive": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Status"}, "displayName": {"type": "cds.String", "@EndUserText.label": "Display Name", "length": 4000}, "workforcePersonProfileId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkforcePersonProfile", "referencedPropertyType": "sap.sf.workforce:WorkforcePersonProfileId"}], "type": "cds.String", "@EndUserText.label": "Person UUID", "length": 32}, "isPrimary": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Primary Assignment"}, "workerType": {"type": "cds.String", "@EndUserText.label": "Worker Type", "length": 128}, "legacyId": {"type": "cds.String", "@EndUserText.label": "User ID", "length": 100}, "assignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:Assignment", "referencedPropertyType": "sap.sf.workforce:AssignmentId"}], "type": "cds.String", "@EndUserText.label": "Assignment UUID", "length": 32}}}}}