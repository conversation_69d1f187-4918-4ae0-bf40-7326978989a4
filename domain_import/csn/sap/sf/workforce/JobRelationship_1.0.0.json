{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/workforce/assignment/additionalinfo/v1/metadata/jobRelationship", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "JobRelationship", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.workforce", "document": {"version": "1.0.0"}}, "definitions": {"JobRelationship": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Job Relationships", "@ODM.entityName": "WorkforceJobRelationship", "@EntityRelationship.entityType": "sap.sf.workforce:JobRelationship", "@EntityRelationship.temporalIds": [{"name": "JobRelationship", "propertyTypes": ["sap.sf.workforce:JobRelationshipId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"id": {"type": "cds.String", "@EndUserText.label": "Assignment UUID", "length": 32, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:JobRelationshipId"}, "jobRelationshipDetails": {"type": "cds.Composition", "target": "JobRelationshipDetail", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Job Relationship Details", "on": [{"ref": ["jobRelationshipDetails", "_id"]}, "=", {"ref": ["id"]}]}, "assignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:Assignment", "referencedPropertyType": "sap.sf.workforce:AssignmentId"}], "type": "cds.String", "@EndUserText.label": "Assignment UUID", "length": 32}}}, "JobRelationshipDetail": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Job Relationship Details", "@EntityRelationship.entityType": "sap.sf.workforce:JobRelationshipDetail", "@EntityRelationship.temporalIds": [{"name": "JobRelationshipDetail", "propertyTypes": ["sap.sf.workforce:JobRelationshipDetailId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_JobRelationship"}, "type": "cds.String", "length": 32, "key": true}, "_JobRelationship": {"type": "cds.Association", "target": "JobRelationship", "cardinality": {"max": 1}, "on": [{"ref": ["_JobRelationship", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.String", "@EndUserText.label": "Encoded user and start date", "length": 256, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:JobRelationshipDetailId"}, "endDate": {"type": "cds.Date", "@EndUserText.label": "End Date"}, "startDate": {"type": "cds.Date", "@EndUserText.label": "Event Date"}, "wfRequestId": {"type": "cds.Integer64", "@EndUserText.label": "wfRequestId"}, "lastModifiedSource": {"type": "cds.String", "@EndUserText.label": "Last Modified Source"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 32}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 32}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 32}, "value": {"type": "cds.Composition", "target": "JobRelationshipDetailGrid", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Job Relationship Details", "on": [{"ref": ["value", "_id"]}, "=", {"ref": ["id"]}]}}}, "JobRelationshipDetailGrid": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Job Relationship Details", "@EntityRelationship.entityType": "sap.sf.workforce:JobRelationshipDetailGrid", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_JobRelationshipDetail"}, "type": "cds.String", "length": 256, "key": true}, "_JobRelationshipDetail": {"type": "cds.Association", "target": "JobRelationshipDetail", "cardinality": {"max": 1}, "on": [{"ref": ["_JobRelationshipDetail", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:JobRelationshipDetailGridId"}, "relationshipType": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:jobRelTypePickList", "referencedPropertyType": "sap.sf.extensibility:jobRelTypePickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Relationship Type", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "relUserId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Name", "length": 384}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "startDate": {"type": "cds.Date", "@EndUserText.label": "Event Date"}, "lastModifiedSource": {"type": "cds.String", "@EndUserText.label": "Last Modified Source"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}, "wfRequestId": {"type": "cds.Integer64", "@EndUserText.label": "wfRequestId"}}}}}