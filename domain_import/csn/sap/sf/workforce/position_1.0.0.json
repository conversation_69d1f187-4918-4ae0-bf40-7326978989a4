{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/workforce/orgstructure/v1/metadata/position", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "Position", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.workforce", "document": {"version": "1.0.0"}}, "definitions": {"Position": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Position", "@ODM.entityName": "WorkforcePosition", "@EntityRelationship.entityType": "sap.sf.workforce:Position", "@EntityRelationship.temporalIds": [{"name": "Position", "propertyTypes": ["sap.sf.workforce:PositionId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Internal ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:PositionId"}, "code": {"type": "cds.String", "@EndUserText.label": "Code", "length": 128}, "timeslices": {"type": "cds.Composition", "target": "PositionTimeSlice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Timeslices", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "PositionTimeSlice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "PositionTimeSlice", "@EntityRelationship.entityType": "sap.sf.workforce:PositionTimeSlice", "@EntityRelationship.temporalIds": [{"name": "PositionTimeSlice", "propertyTypes": ["sap.sf.workforce:PositionTimeSliceRecordId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Position"}, "type": "cds.Integer64", "key": true}, "_Position": {"type": "cds.Association", "target": "Position", "cardinality": {"max": 1}, "on": [{"ref": ["_Position", "id"]}, "=", {"ref": ["_id"]}]}, "externalName": {"type": "cds.String", "@EndUserText.label": "Position Title", "length": 255}, "effectiveStatus": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "validFrom": {"type": "cds.Date", "@EndUserText.label": "Position Start Date"}, "validTo": {"type": "cds.Date", "@EndUserText.label": "End Date"}, "type": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:positionType", "referencedPropertyType": "sap.sf.workforce:positionTypeId"}], "@EndUserText.label": "Id"}, "typeId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:PositionType", "referencedPropertyType": "sap.sf.workforce:PositionTypeId"}], "type": "cds.Integer64", "@EndUserText.label": "Type"}, "positionTitle": {"type": "cds.String", "@EndUserText.label": "Position Title", "length": 255}, "criticality": {"type": "cds.Integer64", "@EndUserText.label": "Criticality", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "comment": {"type": "cds.String", "@EndUserText.label": "Comment", "length": 255}, "incumbent": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "@EndUserText.label": "Id"}, "incumbentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Incumbent", "length": 100}, "changeReason": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueId"}, {"referencedEntityType": "sap.sf.extensibility:ChangeReason1PickList", "referencedPropertyType": "sap.sf.extensibility:ChangeReason1PickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Change Reason"}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 255}, "jobTitle": {"type": "cds.String", "@EndUserText.label": "Job Title", "length": 255}, "jobCode": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:jobClassification", "referencedPropertyType": "sap.sf.foundationobjects:jobClassificationId"}], "@EndUserText.label": "Id"}, "jobCodeId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:JobClassification", "referencedPropertyType": "sap.sf.foundationobjects:JobClassificationId"}], "type": "cds.Integer64", "@EndUserText.label": "Job Code"}, "jobLevel": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueId"}, {"referencedEntityType": "sap.sf.extensibility:JobLevelPickList", "referencedPropertyType": "sap.sf.extensibility:JobLevelPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Job Level"}, "employeeClass": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueId"}, {"referencedEntityType": "sap.sf.extensibility:EMPLOYEECLASSPickList", "referencedPropertyType": "sap.sf.extensibility:EMPLOYEECLASSPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Employee Class"}, "regularTemporary": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueId"}, {"referencedEntityType": "sap.sf.extensibility:RegularTemporaryPickList", "referencedPropertyType": "sap.sf.extensibility:RegularTemporaryPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Regular/Temporary"}, "payGrade": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:payGrade", "referencedPropertyType": "sap.sf.foundationobjects:payGradeId"}], "@EndUserText.label": "Id"}, "payGradeId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:PayGrade", "referencedPropertyType": "sap.sf.foundationobjects:PayGradeId"}], "type": "cds.Integer64", "@EndUserText.label": "Pay Grade"}, "targetFTE": {"type": "cds.Decimal", "@EndUserText.label": "FTE", "precision": 34, "scale": 17}, "vacant": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "To Be <PERSON>"}, "company": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:legalEntity", "referencedPropertyType": "sap.sf.foundationobjects:legalEntityId"}], "@EndUserText.label": "Id"}, "companyId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:LegalEntity", "referencedPropertyType": "sap.sf.foundationobjects:LegalEntityId"}], "type": "cds.Integer64", "@EndUserText.label": "Company"}, "businessUnit": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:businessUnit", "referencedPropertyType": "sap.sf.foundationobjects:businessUnitId"}], "@EndUserText.label": "Id"}, "businessUnitId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:BusinessUnit", "referencedPropertyType": "sap.sf.foundationobjects:BusinessUnitId"}], "type": "cds.Integer64", "@EndUserText.label": "Business Unit"}, "division": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:division", "referencedPropertyType": "sap.sf.foundationobjects:divisionId"}], "@EndUserText.label": "Id"}, "divisionId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Division", "referencedPropertyType": "sap.sf.foundationobjects:DivisionId"}], "type": "cds.Integer64", "@EndUserText.label": "Division"}, "department": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:department", "referencedPropertyType": "sap.sf.foundationobjects:departmentId"}], "@EndUserText.label": "Id"}, "departmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Department", "referencedPropertyType": "sap.sf.foundationobjects:DepartmentId"}], "type": "cds.Integer64", "@EndUserText.label": "Department"}, "location": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:location", "referencedPropertyType": "sap.sf.foundationobjects:locationId"}], "@EndUserText.label": "Id"}, "locationId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Location", "referencedPropertyType": "sap.sf.foundationobjects:LocationId"}], "type": "cds.Integer64", "@EndUserText.label": "Location"}, "costCenter": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:costCenter", "referencedPropertyType": "sap.sf.foundationobjects:costCenterId"}], "@EndUserText.label": "Id"}, "costCenterId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:CostCenter", "referencedPropertyType": "sap.sf.foundationobjects:CostCenterId"}], "type": "cds.Integer64", "@EndUserText.label": "Cost Center"}, "multipleIncumbentsAllowed": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Mass Position"}, "legacyPositionId": {"type": "cds.Integer64", "@EndUserText.label": "legacyPositionId", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "positionControlled": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Subject to Position Control"}, "technicalParameters": {"type": "cds.String", "@EndUserText.label": "technicalParameters", "length": 255}, "positionCriticality": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueId"}, {"referencedEntityType": "sap.sf.extensibility:PositionCriticalityPickList", "referencedPropertyType": "sap.sf.extensibility:PositionCriticalityPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Position Criticality"}, "standardHours": {"type": "cds.Decimal", "@EndUserText.label": "Standard Weekly Hours", "precision": 34, "scale": 17}, "payRange": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:payRange", "referencedPropertyType": "sap.sf.foundationobjects:payRangeId"}], "@EndUserText.label": "Id"}, "payRangeId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:PayRange", "referencedPropertyType": "sap.sf.foundationobjects:PayRangeId"}], "type": "cds.Integer64", "@EndUserText.label": "Pay Range"}, "creationSource": {"type": "cds.String", "@EndUserText.label": "Source of Creation", "length": 255}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Creation Date"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Changed By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Change Date"}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:PositionTimeSliceRecordId"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "positionMatrixRelationship": {"type": "cds.Composition", "target": "PositionMatrixRelationship", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Matrix Relationship", "on": [{"ref": ["positionMatrixRelationship", "_recordId"]}, "=", {"ref": ["recordId"]}]}, "parentPosition": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:position", "referencedPropertyType": "sap.sf.workforce:positionId"}], "@EndUserText.label": "Id"}, "parentPositionId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:Position", "referencedPropertyType": "sap.sf.workforce:PositionId"}], "type": "cds.Integer64", "@EndUserText.label": "parentPosition"}, "texts": {"type": "cds.Composition", "target": "PositionText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_recordId"]}, "=", {"ref": ["recordId"]}]}}}, "PositionMatrixRelationship": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Matrix Relationship For Position", "@EntityRelationship.entityType": "sap.sf.workforce:PositionMatrixRelationship", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PositionTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_PositionTimeSlice": {"type": "cds.Association", "target": "PositionTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_PositionTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "matrixRelationshipType": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueId"}, {"referencedEntityType": "sap.sf.extensibility:PositionMatrixRelationshipTypePickList", "referencedPropertyType": "sap.sf.extensibility:PositionMatrixRelationshipTypePickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Type"}, "id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "relatedPosition": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:position", "referencedPropertyType": "sap.sf.workforce:positionId"}], "@EndUserText.label": "Id"}, "relatedPositionId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:Position", "referencedPropertyType": "sap.sf.workforce:PositionId"}], "type": "cds.Integer64", "@EndUserText.label": "Related Position"}, "entityId": {"type": "cds.String", "@EndUserText.label": "Entity ID", "length": 255}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:PositionMatrixRelationshipRecordId"}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "objectType": {"type": "cds.String", "@EndUserText.label": "Object Type", "length": 255}, "createdAssignment": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "@EndUserText.label": "Id"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:WorkProfile", "referencedPropertyType": "sap.sf.extensibility:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}}}, "PositionText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized value for translatable data type field", "@EntityRelationship.entityType": "sap.sf.workforce:PositionText", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PositionTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_PositionTimeSlice": {"type": "cds.Association", "target": "PositionTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_PositionTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "length": 128, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:PositionTextLocale"}, "externalName": {"type": "cds.String", "@EndUserText.label": "Position Title", "length": 255}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "default"}}}}}