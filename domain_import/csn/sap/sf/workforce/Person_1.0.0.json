{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/workforce/v1/metadata/person", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "Person", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.workforce", "document": {"version": "1.0.0"}}, "definitions": {"Person": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Biographical Information", "@ODM.entityName": "WorkforcePerson", "@ODM.oid": "oid", "@EntityRelationship.entityType": "sap.sf.workforce:Person", "@EntityRelationship.temporalIds": [{"name": "Person", "propertyTypes": ["sap.sf.workforce:PersonId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"id": {"type": "cds.String", "@EndUserText.label": "Per Person UUID", "length": 32, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:PersonId"}, "internalId": {"type": "cds.Integer64", "@EndUserText.label": "Person ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "externalId": {"type": "cds.String", "@EndUserText.label": "Person Id", "length": 32}, "oid": {"type": "cds.String", "@EndUserText.label": "SAP One Domain Model Person ID", "length": 128}, "biographicalDetail": {"type": "cds.Composition", "target": "BiographicalDetail", "cardinality": {"max": 1}, "@EndUserText.label": "Biographical Information", "on": [{"ref": ["biographicalDetail", "_id"]}, "=", {"ref": ["id"]}]}, "personalDetails": {"type": "cds.Composition", "target": "PersonalDetail", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Personal Information", "on": [{"ref": ["personalDetails", "_id"]}, "=", {"ref": ["id"]}]}, "nationalIds": {"type": "cds.Composition", "target": "NationalId", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "National ID Information", "on": [{"ref": ["nationalIds", "_id"]}, "=", {"ref": ["id"]}]}}}, "BiographicalDetail": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Biographical Information", "@EntityRelationship.entityType": "sap.sf.workforce:BiographicalDetail", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Person"}, "type": "cds.String", "length": 32, "key": true}, "_Person": {"type": "cds.Association", "target": "Person", "cardinality": {"max": 1}, "on": [{"ref": ["_Person", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:BiographicalDetailId"}, "personIdExternal": {"type": "cds.String", "@EndUserText.label": "Person Id", "length": 32}, "dateOfBirth": {"type": "cds.Date", "@EndUserText.label": "Date Of Birth"}, "countryOfBirth": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Country", "referencedPropertyType": "sap.sf.foundationobjects:CountryCode"}], "type": "cds.String", "@EndUserText.label": "Country/Region Of Birth", "length": 100}, "regionOfBirth": {"type": "cds.String", "@EndUserText.label": "Region Of Birth", "length": 100}, "placeOfBirth": {"type": "cds.String", "@EndUserText.label": "Place Of Birth", "length": 100}, "dateOfDeath": {"type": "cds.Date", "@EndUserText.label": "Date of Death"}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}, "biographicalLocDetails": {"type": "cds.Composition", "target": "BiographicalLocDetail", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Biographical Information", "on": [{"ref": ["biographicalLocDetails", "_id"]}, "=", {"ref": ["id"]}]}}}, "PersonalDetail": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Personal Information", "@EntityRelationship.entityType": "sap.sf.workforce:PersonalDetail", "@EntityRelationship.temporalIds": [{"name": "PersonalDetail", "propertyTypes": ["sap.sf.workforce:PersonalDetailId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "startDate", "temporalIntervalEndProperty": "endDate"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Person"}, "type": "cds.String", "length": 32, "key": true}, "_Person": {"type": "cds.Association", "target": "Person", "cardinality": {"max": 1}, "on": [{"ref": ["_Person", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:PersonalDetailId"}, "firstName": {"type": "cds.String", "@EndUserText.label": "First Name", "length": 128}, "lastName": {"type": "cds.String", "@EndUserText.label": "Last Name", "length": 128}, "secondLastName": {"type": "cds.String", "@EndUserText.label": "Second Last Name", "length": 128}, "middleName": {"type": "cds.String", "@EndUserText.label": "Middle Name", "length": 128}, "salutation": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:salutationPickList", "referencedPropertyType": "sap.sf.extensibility:salutationPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Salutation", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "suffix": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:namesuffixPickList", "referencedPropertyType": "sap.sf.extensibility:namesuffixPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Suffix", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "firstNameAlt1": {"type": "cds.String", "@EndUserText.label": "First Name", "length": 128}, "middleNameAlt1": {"type": "cds.String", "@EndUserText.label": "Middle Name", "length": 128}, "lastNameAlt1": {"type": "cds.String", "@EndUserText.label": "Last Name", "length": 128}, "firstNameAlt2": {"type": "cds.String", "@EndUserText.label": "First Name", "length": 128}, "middleNameAlt2": {"type": "cds.String", "@EndUserText.label": "Middle Name", "length": 128}, "lastNameAlt2": {"type": "cds.String", "@EndUserText.label": "Last Name", "length": 128}, "displayName": {"type": "cds.String", "@EndUserText.label": "Display Name", "length": 128}, "formalName": {"type": "cds.String", "@EndUserText.label": "Formal Name", "length": 128}, "title": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:titlePickList", "referencedPropertyType": "sap.sf.extensibility:titlePickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Title", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "birthName": {"type": "cds.String", "@EndUserText.label": "Birth Name", "length": 128}, "birthNameAlt1": {"type": "cds.String", "@EndUserText.label": "Birth Name", "length": 128}, "birthNameAlt2": {"type": "cds.String", "@EndUserText.label": "Birth Name", "length": 128}, "preferredName": {"type": "cds.String", "@EndUserText.label": "Preferred Name", "length": 128}, "initials": {"type": "cds.String", "@EndUserText.label": "Initials", "length": 128}, "secondTitle": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:secondTitlePickList", "referencedPropertyType": "sap.sf.extensibility:secondTitlePickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Second Title", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "namePrefix": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:nameprefixPickList", "referencedPropertyType": "sap.sf.extensibility:nameprefixPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Prefix", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "displayNameAlt1": {"type": "cds.String", "@EndUserText.label": "Display Name", "length": 128}, "displayNameAlt2": {"type": "cds.String", "@EndUserText.label": "Display Name", "length": 128}, "formalNameAlt1": {"type": "cds.String", "@EndUserText.label": "Formal Name", "length": 128}, "formalNameAlt2": {"type": "cds.String", "@EndUserText.label": "Formal Name", "length": 128}, "customString1": {"type": "cds.String", "@EndUserText.label": "Preferred Name", "length": 128, "@ObjectModel.custom": true}, "gender": {"type": "cds.String", "@EndUserText.label": "Gender", "length": 2}, "maritalStatus": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:ecMaritalStatusPickList", "referencedPropertyType": "sap.sf.extensibility:ecMaritalStatusPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Marital Status", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "since": {"type": "cds.Date", "@EndUserText.label": "Marital Status Since"}, "nationality": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Country", "referencedPropertyType": "sap.sf.foundationobjects:CountryCode"}], "type": "cds.String", "@EndUserText.label": "Nationality", "length": 128}, "secondNationality": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Country", "referencedPropertyType": "sap.sf.foundationobjects:CountryCode"}], "type": "cds.String", "@EndUserText.label": "Second Nationality", "length": 128}, "thirdNationality": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Country", "referencedPropertyType": "sap.sf.foundationobjects:CountryCode"}], "type": "cds.String", "@EndUserText.label": "Third Nationality", "length": 128}, "nativePreferredLang": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:languagePickList", "referencedPropertyType": "sap.sf.extensibility:languagePickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Native Preferred Language", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "challengeStatus": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Challenge Status"}, "certificateStartDate": {"type": "cds.Date", "@EndUserText.label": "Certificate Start Date"}, "certificateEndDate": {"type": "cds.Date", "@EndUserText.label": "Certificate End Date"}, "partnerName": {"type": "cds.String", "@EndUserText.label": "Partner Name", "length": 128}, "partnerNamePrefix": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:nameprefixPickList", "referencedPropertyType": "sap.sf.extensibility:nameprefixPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Partner Name Prefix", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "nameFormatCode": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:NameFormatGO", "referencedPropertyType": "sap.sf.foundationobjects:NameFormatGOId"}], "type": "cds.Integer64", "@EndUserText.label": "Name Format Code", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "script": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Script", "referencedPropertyType": "sap.sf.foundationobjects:ScriptId"}], "type": "cds.Integer64", "@EndUserText.label": "Language Script", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "scriptAlt1": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Script", "referencedPropertyType": "sap.sf.foundationobjects:ScriptId"}], "type": "cds.Integer64", "@EndUserText.label": "Language Script", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "scriptAlt2": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Script", "referencedPropertyType": "sap.sf.foundationobjects:ScriptId"}], "type": "cds.Integer64", "@EndUserText.label": "Language Script", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "thirdName": {"type": "cds.String", "@EndUserText.label": "Third Name", "length": 128}, "thirdNameAlt1": {"type": "cds.String", "@EndUserText.label": "Third Name", "length": 128}, "thirdNameAlt2": {"type": "cds.String", "@EndUserText.label": "Third Name", "length": 128}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "startDate": {"type": "cds.Date", "@EndUserText.label": "Event Date"}, "endDate": {"type": "cds.Date", "@EndUserText.label": "End Date"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}, "wfRequestId": {"type": "cds.Integer64", "@EndUserText.label": "wfRequestId"}, "globalDetails": {"type": "cds.Composition", "target": "GlobalDetail", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Global Information", "on": [{"ref": ["globalDetails", "_id"]}, "=", {"ref": ["id"]}]}}}, "NationalId": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "National ID Information", "@EntityRelationship.entityType": "sap.sf.workforce:NationalId", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Person"}, "type": "cds.String", "length": 32, "key": true}, "_Person": {"type": "cds.Association", "target": "Person", "cardinality": {"max": 1}, "on": [{"ref": ["_Person", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:NationalIdId"}, "country": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Country", "referencedPropertyType": "sap.sf.foundationobjects:CountryCode"}], "type": "cds.String", "@EndUserText.label": "Country/Region", "length": 100}, "cardType": {"type": "cds.String", "@EndUserText.label": "National Id Card Type", "length": 256}, "nationalId": {"type": "cds.String", "@EndUserText.label": "National Id", "length": 256}, "isPrimary": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Primary"}, "isTemporary": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Temporary ID Available"}, "temporaryId": {"type": "cds.String", "@EndUserText.label": "Temporary ID", "length": 256}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "notes": {"type": "cds.String", "@EndUserText.label": "Notes", "length": 4000}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}}}, "BiographicalLocDetail": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized Biographical Information", "@EntityRelationship.entityType": "sap.sf.workforce:BiographicalLocDetail", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_BiographicalDetail"}, "type": "cds.Integer64", "key": true}, "_BiographicalDetail": {"type": "cds.Association", "target": "BiographicalDetail", "cardinality": {"max": 1}, "on": [{"ref": ["_BiographicalDetail", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:BiographicalLocDetailId"}, "countryCode": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Country", "referencedPropertyType": "sap.sf.foundationobjects:CountryCode"}], "type": "cds.String", "@EndUserText.label": "Country Code", "length": 3}, "regionOfBirth": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:STATE_BRAPickList", "referencedPropertyType": "sap.sf.extensibility:STATE_BRAPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Region Of Birth", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "placeOfBirth": {"type": "cds.String", "@EndUserText.label": "Place Of Birth", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "country": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:country", "referencedPropertyType": "sap.sf.foundationobjects:countryId"}], "type": "cds.Integer64", "@EndUserText.label": "Country/Region", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}}}, "GlobalDetail": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Global Information", "@EntityRelationship.entityType": "sap.sf.workforce:GlobalDetail", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_PersonalDetail"}, "type": "cds.Integer64", "key": true}, "_PersonalDetail": {"type": "cds.Association", "target": "PersonalDetail", "cardinality": {"max": 1}, "on": [{"ref": ["_PersonalDetail", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:GlobalDetailId"}, "countryCode": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Country", "referencedPropertyType": "sap.sf.foundationobjects:CountryCode"}], "type": "cds.String", "@EndUserText.label": "Country Code", "length": 3}, "genderCountrySpecific": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:gender_USAPickList", "referencedPropertyType": "sap.sf.extensibility:gender_USAPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Gender", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "genericString1": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:ETHNICGROUP_USAPickList", "referencedPropertyType": "sap.sf.extensibility:ETHNICGROUP_USAPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Ethnic Group", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericString14": {"type": "cds.String", "@EndUserText.label": "Medicare Beneficiary Identifier (MBI)", "length": 11, "@ObjectModel.custom": true}, "genericString8": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:yesNoPickList", "referencedPropertyType": "sap.sf.extensibility:yesNoPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Medicare", "@Semantics.valueRange": {"minimum": "-99999999999999", "maximum": "999999999999999"}, "@ObjectModel.custom": true}, "genericString15": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:yesNoPickList", "referencedPropertyType": "sap.sf.extensibility:yesNoPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Equal Employment Opportunity (EEO)", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericString13": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:veteranCategory_USAPickList", "referencedPropertyType": "sap.sf.extensibility:veteranCategory_USAPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Disclosed Veteran Category", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericNumber2": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:yesNoPickList", "referencedPropertyType": "sap.sf.extensibility:yesNoPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Challenged Veteran", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericNumber3": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:yesNoPickList", "referencedPropertyType": "sap.sf.extensibility:yesNoPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Veterans (who served on active duty in war or campaign) authorized with a campaign badge", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericNumber4": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:yesNoPickList", "referencedPropertyType": "sap.sf.extensibility:yesNoPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Armed Forces Medal Veteran", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericNumber5": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:yesNoPickList", "referencedPropertyType": "sap.sf.extensibility:yesNoPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Recently Separated Veteran", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericDate3": {"type": "cds.Date", "@EndUserText.label": " Date of Separation from Military Service ", "@ObjectModel.custom": true}, "genericNumber6": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:yesNoPickList", "referencedPropertyType": "sap.sf.extensibility:yesNoPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Special Disabled <PERSON><PERSON>an", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericNumber7": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:yesNoPickList", "referencedPropertyType": "sap.sf.extensibility:yesNoPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Vietnam Era Veteran", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericNumber8": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:yesNoPickList", "referencedPropertyType": "sap.sf.extensibility:yesNoPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Other Disabled Veteran", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericNumber9": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:VISATYPE_USAPickList", "referencedPropertyType": "sap.sf.extensibility:VISATYPE_USAPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Visa Type", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericString2": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:challengeGroupPickList", "referencedPropertyType": "sap.sf.extensibility:challengeGroupPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Challenge Group", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericNumber10": {"type": "cds.Integer64", "@EndUserText.label": "Degree of Challenge", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericString3": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:typeOfChallengePickList", "referencedPropertyType": "sap.sf.extensibility:typeOfChallengePickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Type of Challenge", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericDate1": {"type": "cds.Date", "@EndUserText.label": "Date Learned", "@ObjectModel.custom": true}, "genericString4": {"type": "cds.String", "@EndUserText.label": "Issuing Authority", "length": 256, "@ObjectModel.custom": true}, "genericString5": {"type": "cds.String", "@EndUserText.label": "Reference Number", "length": 256, "@ObjectModel.custom": true}, "genericNumber12": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:SelfIdentificationDisabilityStatusUSAPickList", "referencedPropertyType": "sap.sf.extensibility:SelfIdentificationDisabilityStatusUSAPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Status Given in Form CC-305", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericDate5": {"type": "cds.Date", "@EndUserText.label": "Submission Date of Form CC-305", "@ObjectModel.custom": true}, "genericString6": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:yesNoPickList", "referencedPropertyType": "sap.sf.extensibility:yesNoPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Challenged", "@Semantics.valueRange": {"minimum": "-99999999999999", "maximum": "999999999999999"}, "@ObjectModel.custom": true}, "genericDate2": {"type": "cds.Date", "@EndUserText.label": "Date of Determination of Challenge Status", "@ObjectModel.custom": true}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "startDate": {"type": "cds.Date", "@EndUserText.label": "Event Date"}, "endDate": {"type": "cds.Date", "@EndUserText.label": "End Date"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}, "genericNumber1": {"type": "cds.Integer64", "@EndUserText.label": "Number of Children", "@Semantics.valueRange": {"minimum": "-9", "maximum": "99"}, "@ObjectModel.custom": true}, "genericString7": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:typeOfChallengePickList", "referencedPropertyType": "sap.sf.extensibility:typeOfChallengePickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Type of Challenge", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericString9": {"type": "cds.String", "@EndUserText.label": "Reference Number", "length": 256, "@ObjectModel.custom": true}, "genericDate6": {"type": "cds.Date", "@EndUserText.label": "Nationality Acquisition Date", "@ObjectModel.custom": true}, "genericString10": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:challengeGroup_BLRPickList", "referencedPropertyType": "sap.sf.extensibility:challengeGroup_BLRPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Challenge Group", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericString11": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:ChallengeCategory_BLRPickList", "referencedPropertyType": "sap.sf.extensibility:ChallengeCategory_BLRPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Challenge Category", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericString12": {"type": "cds.String", "@EndUserText.label": "Challenge Reason", "length": 256, "@ObjectModel.custom": true}, "genericDate4": {"type": "cds.Date", "@EndUserText.label": "Expiration Date", "@ObjectModel.custom": true}, "genericNumber14": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:yesNoPickList", "referencedPropertyType": "sap.sf.extensibility:yesNoPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Foreign Employee with Brazilian Spouse", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericNumber15": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:yesNoPickList", "referencedPropertyType": "sap.sf.extensibility:yesNoPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Foreign Employee with Brazilian Children", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericString19": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:residenceTime_BRAPickList", "referencedPropertyType": "sap.sf.extensibility:residenceTime_BRAPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Residence Period of Foreign Employee", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericDate10": {"type": "cds.Date", "@EndUserText.label": "Retirement for INSS", "@ObjectModel.custom": true}, "genericNumber16": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:yesNoPickList", "referencedPropertyType": "sap.sf.extensibility:yesNoPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Receiving Unemployment Insurance Benefit", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericString16": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:Disability_Type_BRAPickList", "referencedPropertyType": "sap.sf.extensibility:Disability_Type_BRAPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Disability Type", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericDate7": {"type": "cds.Date", "@EndUserText.label": "Date of Naturalization", "@ObjectModel.custom": true}, "genericString21": {"type": "cds.String", "@EndUserText.label": "Birth First Name", "length": 256, "@ObjectModel.custom": true}, "genericString22": {"type": "cds.String", "@EndUserText.label": "Birth Last Name", "length": 256, "@ObjectModel.custom": true}, "genericString17": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:MILITARY_STATUS_EGYPickList", "referencedPropertyType": "sap.sf.extensibility:MILITARY_STATUS_EGYPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Military Certificate Status", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericString18": {"type": "cds.String", "@EndUserText.label": "Military Occupational Specialty (MOS)", "length": 40, "@ObjectModel.custom": true}, "genericString20": {"type": "cds.String", "@EndUserText.label": "Reason for Exemption", "length": 40, "@ObjectModel.custom": true}, "genericNumber11": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:yesNoPickList", "referencedPropertyType": "sap.sf.extensibility:yesNoPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Other Disabled Veteran", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericString23": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:yesNoPickList", "referencedPropertyType": "sap.sf.extensibility:yesNoPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Retirement Age Exemption", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericString24": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:NonRegisteredReason_SAUPickList", "referencedPropertyType": "sap.sf.extensibility:NonRegisteredReason_SAUPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Non-Registered Reason", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}, "genericString25": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:yesNoPickList", "referencedPropertyType": "sap.sf.extensibility:yesNoPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Uses Company Transportation", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@ObjectModel.custom": true}}}}}