{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/workforce/assignment/compensation/v1/metadata/oneTimePayment", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "OneTimePayment", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.workforce", "document": {"version": "1.0.0"}}, "definitions": {"OneTimePayment": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Spot Bonus", "@ODM.entityName": "WorkforceOneTimePayment", "@EntityRelationship.entityType": "sap.sf.workforce:OneTimePayment", "@EntityRelationship.entityIds": [{"name": "OneTimePayment", "propertyTypes": ["sap.sf.workforce:OneTimePaymentId"]}], "elements": {"id": {"type": "cds.String", "@EndUserText.label": "Assignment UUID", "length": 32, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:OneTimePaymentId"}, "oneTimePaymentDetails": {"type": "cds.Composition", "target": "OneTimePaymentDetail", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Spot Bonus", "on": [{"ref": ["oneTimePaymentDetails", "_id"]}, "=", {"ref": ["id"]}]}, "assignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:Assignment", "referencedPropertyType": "sap.sf.workforce:AssignmentId"}], "type": "cds.String", "@EndUserText.label": "Assignment UUID", "length": 32}}}, "OneTimePaymentDetail": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Spot Bonus", "@EntityRelationship.entityType": "sap.sf.workforce:OneTimePaymentDetail", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_OneTimePayment"}, "type": "cds.String", "length": 32, "key": true}, "_OneTimePayment": {"type": "cds.Association", "target": "OneTimePayment", "cardinality": {"max": 1}, "on": [{"ref": ["_OneTimePayment", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:OneTimePaymentDetailId"}, "payDate": {"type": "cds.Date", "@EndUserText.label": "Issue Date"}, "payComponentCode": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:PayComponent", "referencedPropertyType": "sap.sf.foundationobjects:PayComponentId"}], "type": "cds.Integer64", "@EndUserText.label": "Pay Component", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "currencyCode": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Currency", "referencedPropertyType": "sap.sf.foundationobjects:CurrencyId"}], "type": "cds.String", "@EndUserText.label": "<PERSON><PERSON><PERSON><PERSON>", "length": 20}, "alternativeCostCenter": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:CostCenter", "referencedPropertyType": "sap.sf.foundationobjects:CostCenterId"}], "type": "cds.Integer64", "@EndUserText.label": "Alternative Cost Center", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "sequenceNumber": {"type": "cds.String", "@EndUserText.label": "Sequence Number"}, "numberOfUnits": {"type": "cds.Decimal", "@EndUserText.label": "Number", "precision": 34, "scale": 3}, "unitOfMeasure": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:UnitOfMeasure", "referencedPropertyType": "sap.sf.foundationobjects:UnitOfMeasureId"}], "type": "cds.Integer64", "@EndUserText.label": "Unit of Measure", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "amount": {"type": "cds.Decimal", "@EndUserText.label": "Amount", "precision": 34, "scale": 3}, "percentage": {"type": "cds.Decimal", "@EndUserText.label": "Percentage", "precision": 34, "scale": 3}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}}}}}