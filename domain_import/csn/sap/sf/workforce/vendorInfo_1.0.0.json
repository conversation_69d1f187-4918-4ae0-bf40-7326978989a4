{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/workforce/contingentmanagement/v1/metadata/vendorInfo", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "VendorInfo", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.workforce", "document": {"version": "1.0.0"}}, "definitions": {"VendorInfo": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "VendorInfo", "@ODM.entityName": "BusinessPartner", "@ODM.oid": "entityOID", "@EntityRelationship.entityType": "sap.sf.workforce:VendorInfo", "@EntityRelationship.temporalIds": [{"name": "VendorInfo", "propertyTypes": ["sap.sf.workforce:VendorInfoId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:VendorInfoId"}, "entityOID": {"type": "cds.String", "@EndUserText.label": "entityOID", "length": 128}, "vendorCode": {"type": "cds.String", "@EndUserText.label": "vendorCode", "length": 128}, "timeslices": {"type": "cds.Composition", "target": "VendorInfoTimeSlice", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Timeslices", "on": [{"ref": ["timeslices", "_id"]}, "=", {"ref": ["id"]}]}}}, "VendorInfoTimeSlice": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "VendorInfoTimeSlice", "@EntityRelationship.entityType": "sap.sf.workforce:VendorInfoTimeSlice", "@EntityRelationship.temporalIds": [{"name": "VendorInfoTimeSlice", "propertyTypes": ["sap.sf.workforce:VendorInfoTimeSliceRecordId"], "temporalIntervalType": "CLOSED_CLOSED", "temporalType": "DATE", "temporalIntervalStartProperty": "validFrom", "temporalIntervalEndProperty": "validTo"}], "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_VendorInfo"}, "type": "cds.Integer64", "key": true}, "_VendorInfo": {"type": "cds.Association", "target": "VendorInfo", "cardinality": {"max": 1}, "on": [{"ref": ["_VendorInfo", "id"]}, "=", {"ref": ["_id"]}]}, "vendorName": {"type": "cds.String", "@EndUserText.label": "vendorName", "length": 128}, "description": {"type": "cds.String", "@EndUserText.label": "description", "length": 128}, "effectiveStatus": {"type": "cds.String", "@EndUserText.label": "effectiveStatus", "length": 128}, "validFrom": {"type": "cds.Date", "@EndUserText.label": "effectiveStartDate"}, "validTo": {"type": "cds.Date", "@EndUserText.label": "Effective End Date"}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:VendorInfoTimeSliceRecordId"}, "createdAssignment": {"type": "cds.String", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "@EndUserText.label": "Id"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "texts": {"type": "cds.Composition", "target": "VendorInfoText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_recordId"]}, "=", {"ref": ["recordId"]}]}}}, "VendorInfoText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Localized value for translatable data type field", "@EntityRelationship.entityType": "sap.sf.workforce:VendorInfoText", "elements": {"_recordId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_VendorInfoTimeSlice"}, "type": "cds.String", "length": 255, "key": true}, "_VendorInfoTimeSlice": {"type": "cds.Association", "target": "VendorInfoTimeSlice", "cardinality": {"max": 1}, "on": [{"ref": ["_VendorInfoTimeSlice", "recordId"]}, "=", {"ref": ["_recordId"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "locale", "length": 128, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:VendorInfoTextLocale"}, "description": {"type": "cds.String", "@EndUserText.label": "description", "length": 128}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "default"}}}}}