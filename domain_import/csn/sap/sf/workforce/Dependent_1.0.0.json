{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/workforce/relatedpersons/v1/metadata/dependent", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "Dependent", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.workforce", "document": {"version": "1.0.0"}}, "definitions": {"Dependent": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Biographical Information", "@ODM.entityName": "WorkforceDependent", "@EntityRelationship.entityType": "sap.sf.workforce:Dependent", "@EntityRelationship.entityIds": [{"name": "Dependent", "propertyTypes": ["sap.sf.workforce:DependentId"]}], "elements": {"id": {"type": "cds.String", "@EndUserText.label": "Per Person UUID", "length": 32, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:DependentId"}, "dependentBiographicalDetails": {"type": "cds.Composition", "target": "DependentBiographicalDetail", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Biographical Information", "on": [{"ref": ["dependentBiographicalDetails", "_id"]}, "=", {"ref": ["id"]}]}, "personId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:Person", "referencedPropertyType": "sap.sf.workforce:PersonId"}], "type": "cds.String", "@EndUserText.label": "Per Person UUID", "length": 32}}}, "DependentBiographicalDetail": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Biographical Information", "@EntityRelationship.entityType": "sap.sf.workforce:DependentBiographicalDetail", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Dependent"}, "type": "cds.String", "length": 32, "key": true}, "_Dependent": {"type": "cds.Association", "target": "Dependent", "cardinality": {"max": 1}, "on": [{"ref": ["_Dependent", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:DependentBiographicalDetailId"}, "dateOfBirth": {"type": "cds.Date", "@EndUserText.label": "Date Of Birth"}, "countryOfBirth": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Country", "referencedPropertyType": "sap.sf.foundationobjects:CountryCode"}], "type": "cds.String", "@EndUserText.label": "Country/Region Of Birth", "length": 100}, "regionOfBirth": {"type": "cds.String", "@EndUserText.label": "Region Of Birth", "length": 100}, "placeOfBirth": {"type": "cds.String", "@EndUserText.label": "Place Of Birth", "length": 100}, "dateOfDeath": {"type": "cds.Date", "@EndUserText.label": "Date of Death"}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}, "dependentPersonalDetails": {"type": "cds.Composition", "target": "DependentPersonalDetail", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Personal Information", "on": [{"ref": ["dependentPersonalDetails", "_id"]}, "=", {"ref": ["id"]}]}, "personRelationships": {"type": "cds.Composition", "target": "PersonRelationship", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Dependents", "on": [{"ref": ["personRelationships", "_id"]}, "=", {"ref": ["id"]}]}}}, "DependentPersonalDetail": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Personal Information", "@EntityRelationship.entityType": "sap.sf.workforce:DependentPersonalDetail", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_DependentBiographicalDetail"}, "type": "cds.Integer64", "key": true}, "_DependentBiographicalDetail": {"type": "cds.Association", "target": "DependentBiographicalDetail", "cardinality": {"max": 1}, "on": [{"ref": ["_DependentBiographicalDetail", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:DependentPersonalDetailId"}, "firstName": {"type": "cds.String", "@EndUserText.label": "First Name", "length": 128}, "lastName": {"type": "cds.String", "@EndUserText.label": "Last Name", "length": 128}, "secondLastName": {"type": "cds.String", "@EndUserText.label": "Second Last Name", "length": 128}, "middleName": {"type": "cds.String", "@EndUserText.label": "Middle Name", "length": 128}, "salutation": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:salutationPickList", "referencedPropertyType": "sap.sf.extensibility:salutationPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Salutation", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "suffix": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:namesuffixPickList", "referencedPropertyType": "sap.sf.extensibility:namesuffixPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Suffix", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "displayName": {"type": "cds.String", "@EndUserText.label": "Display Name", "length": 128}, "formalName": {"type": "cds.String", "@EndUserText.label": "Formal Name", "length": 128}, "birthName": {"type": "cds.String", "@EndUserText.label": "Birth Name", "length": 128}, "initials": {"type": "cds.String", "@EndUserText.label": "Initials", "length": 128}, "namePrefix": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:nameprefixPickList", "referencedPropertyType": "sap.sf.extensibility:nameprefixPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Prefix", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "customString1": {"type": "cds.String", "@EndUserText.label": "Preferred Name", "length": 128, "@ObjectModel.custom": true}, "gender": {"type": "cds.String", "@EndUserText.label": "Gender", "length": 2}, "maritalStatus": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:ecMaritalStatusPickList", "referencedPropertyType": "sap.sf.extensibility:ecMaritalStatusPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Marital Status", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "since": {"type": "cds.Date", "@EndUserText.label": "Marital Status Since"}, "nationality": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Country", "referencedPropertyType": "sap.sf.foundationobjects:CountryCode"}], "type": "cds.String", "@EndUserText.label": "Nationality", "length": 128}, "secondNationality": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Country", "referencedPropertyType": "sap.sf.foundationobjects:CountryCode"}], "type": "cds.String", "@EndUserText.label": "Second Nationality", "length": 128}, "thirdNationality": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Country", "referencedPropertyType": "sap.sf.foundationobjects:CountryCode"}], "type": "cds.String", "@EndUserText.label": "Third Nationality", "length": 128}, "script": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:Script", "referencedPropertyType": "sap.sf.foundationobjects:ScriptId"}], "type": "cds.Integer64", "@EndUserText.label": "Language Script", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "thirdName": {"type": "cds.String", "@EndUserText.label": "Third Name", "length": 128}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "startDate": {"type": "cds.Date", "@EndUserText.label": "Event Date"}, "endDate": {"type": "cds.Date", "@EndUserText.label": "End Date"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}}}, "PersonRelationship": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Dependents", "@EntityRelationship.entityType": "sap.sf.workforce:PersonRelationship", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_DependentBiographicalDetail"}, "type": "cds.Integer64", "key": true}, "_DependentBiographicalDetail": {"type": "cds.Association", "target": "DependentBiographicalDetail", "cardinality": {"max": 1}, "on": [{"ref": ["_DependentBiographicalDetail", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.workforce:PersonRelationshipId"}, "relationshipType": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueOptionId"}, {"referencedEntityType": "sap.sf.extensibility:personRelationshipTypePickList", "referencedPropertyType": "sap.sf.extensibility:personRelationshipTypePickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Relationship", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "isAccompanyingDependent": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Accompanying"}, "isAddressSameAsPerson": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Copy Address from Employee"}, "isBeneficiary": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Is Beneficiary"}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "createdAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified On"}, "modifiedAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:WorkProfile", "referencedPropertyType": "sap.sf.workforce:WorkProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "startDate": {"type": "cds.Date", "@EndUserText.label": "Event Date"}, "endDate": {"type": "cds.Date", "@EndUserText.label": "End Date"}, "modifiedOnBehalfOfAssignmentId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "on behalf of assignment UUID", "length": 32}}}}}