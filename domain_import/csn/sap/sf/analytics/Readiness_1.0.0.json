{"definitions": {"Readiness": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:Readiness", "@EntityRelationship.entityIds": [{"name": "Readiness", "propertyTypes": ["sap.sf.analytics:ReadinessReadinessCode"]}], "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_DIMENSION"}, "@ObjectModel.supportedCapabilities": [{"#": "ANALYTICAL_DIMENSION"}], "elements": {"readinessCode": {"@EndUserText.label": "Readiness Code", "key": true, "type": "cds.String", "notNull": true}, "readinessLabel": {"@EndUserText.label": "Readiness Label", "type": "cds.String"}, "category": {"@EndUserText.label": "Category", "type": "cds.String"}, "readinessRank": {"@EndUserText.label": "Readiness Rank", "type": "cds.Integer"}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "Readiness", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}