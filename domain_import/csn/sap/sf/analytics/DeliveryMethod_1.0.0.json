{"definitions": {"DeliveryMethod": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:DeliveryMethod", "@EntityRelationship.entityIds": [{"name": "DeliveryMethod", "propertyTypes": ["sap.sf.analytics:DeliveryMethodDEL_MTH_ID"]}], "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_DIMENSION"}, "@ObjectModel.supportedCapabilities": [{"#": "ANALYTICAL_DIMENSION"}], "elements": {"DeliveryMethodID": {"@EndUserText.label": "ID", "@EntityRelationship.propertyType": "sap.sf.analytics:DeliveryMethodDEL_MTH_ID", "key": true, "type": "cds.String", "length": 256, "notNull": true}, "DeliveryMethodDescription": {"@EndUserText.label": "Description", "type": "cds.String", "length": 256}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "DeliveryMethod", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}