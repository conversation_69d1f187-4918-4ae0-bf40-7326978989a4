{"definitions": {"SuccessionData": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:SuccessionData", "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_FACT"}, "@ObjectModel.supportedCapabilities": [{"#": "CDS_MODELING_DATA_SOURCE"}, {"#": "ANALYTICAL_PROVIDER"}], "elements": {"nominationId": {"@EndUserText.label": "Nomination Id", "key": true, "type": "cds.Integer64", "notNull": true}, "incumbentId": {"@EndUserText.label": "Incumbent Id", "type": "cds.String"}, "nomineeId": {"@EndUserText.label": "Nominee Id", "key": true, "type": "cds.Integer64", "notNull": true}, "successorId": {"@EndUserText.label": "Successor Id", "type": "cds.String"}, "externalSuccessorId": {"@EndUserText.label": "External Successor Id", "type": "cds.Integer64"}, "poolId": {"@EndUserText.label": "Pool Id", "type": "cds.Integer64"}, "positionId": {"@EndUserText.label": "Position Id", "type": "cds.Integer64"}, "nominationTypeId": {"@EndUserText.label": "Nomination Type Id", "type": "cds.Integer64"}, "successionReadinessId": {"@EndUserText.label": "Succession Readiness Id", "type": "cds.String"}, "successorStatusId": {"@EndUserText.label": "Successor Status Id", "type": "cds.Integer64"}, "nomineeTypeId": {"@EndUserText.label": "Nominee Type Id", "type": "cds.String"}, "highPotentialId": {"@EndUserText.label": "High Potential Id", "type": "cds.Decimal", "precision": 34, "scale": 1}, "effectiveStartDt": {"@EndUserText.label": "Start Date", "key": true, "type": "cds.Date", "notNull": true}, "effectveEndDt": {"@EndUserText.label": "End Date", "type": "cds.Date"}, "createdAt": {"@EndUserText.label": "Created At", "type": "cds.Date"}, "maxDate": {"@EndUserText.label": "Max Date", "type": "cds.Date"}, "highestSuccessionReadinessId": {"@EndUserText.label": "Highest Succession Readiness Id", "type": "cds.String"}, "isEmergencyCover": {"@EndUserText.label": "Is Emergency Cover", "type": "cds.<PERSON><PERSON><PERSON>"}, "eopNominations": {"@EndUserText.label": "End Of Period Nominations", "type": "cds.Double"}, "eopTalentPoolNominations": {"@EndUserText.label": "End Of Period Talent Pool Nominations", "type": "cds.Double"}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "SuccessionData", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}