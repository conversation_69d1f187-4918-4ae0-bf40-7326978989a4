{"definitions": {"ApplicationStatus": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:ApplicationStatus", "@EntityRelationship.entityIds": [{"name": "ApplicationStatus", "propertyTypes": ["sap.sf.analytics:ApplicationStatusChldNode"]}], "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_DIMENSION"}, "@ObjectModel.supportedCapabilities": [{"#": "ANALYTICAL_DIMENSION"}], "elements": {"Id": {"@EndUserText.label": "Id", "@EntityRelationship.propertyType": "sap.sf.analytics:ApplicationStatusChldNode", "key": true, "type": "cds.Integer64", "notNull": true}, "name": {"@EndUserText.label": "Label", "type": "cds.String"}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "ApplicationStatus", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}