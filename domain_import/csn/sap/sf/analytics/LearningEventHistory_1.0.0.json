{"definitions": {"LearningEventHistory": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:LearningEventHistory", "@EntityRelationship.entityIds": [{"name": "LearningEventHistory", "propertyTypes": ["sap.sf.analytics:StudentID", "sap.sf.analytics:LearningItemID", "sap.sf.analytics:ItemCompletionStatusID", "sap.sf.analytics:CompletionDateTime"]}], "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_FACT"}, "@ObjectModel.supportedCapabilities": [{"#": "CDS_MODELING_DATA_SOURCE"}, {"#": "ANALYTICAL_PROVIDER"}], "elements": {"PersonGuid": {"@EndUserText.label": "Person ID", "type": "cds.String"}, "StudentID": {"@EndUserText.label": "User ID", "key": true, "type": "cds.String", "notNull": true}, "ItemID": {"@EndUserText.label": "Component ID", "type": "cds.String"}, "ItemTypeID": {"@EndUserText.label": "Course Type ID", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.learning:CourseType", "referencedPropertyType": "sap.sf.learning:CourseTypeId"}], "type": "cds.String"}, "RevisionDate": {"@EndUserText.label": "Revision Date", "type": "cds.DateTime"}, "LearningItemID": {"@EndUserText.label": "Learning Entity ID", "key": true, "type": "cds.String", "notNull": true}, "LearningItemType": {"@EndUserText.label": "Learning Entity Type", "type": "cds.String"}, "CourseID": {"@EndUserText.label": "Course ID", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.learning:Course", "referencedPropertyType": "sap.sf.learning:CourseId"}], "type": "cds.String"}, "ProgramID": {"@EndUserText.label": "Program ID", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.learning:Program", "referencedPropertyType": "sap.sf.learning:ProgramId"}], "type": "cds.String"}, "ItemCompletionStatusID": {"@EndUserText.label": "Completion Status", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.learning:CompletionStatus", "referencedPropertyType": "sap.sf.learning:CompletionStatusId"}], "key": true, "type": "cds.String", "notNull": true}, "CompletionDateTime": {"@EndUserText.label": "Completion Date", "key": true, "type": "cds.DateTime", "notNull": true}, "ItemDuration": {"@EndUserText.label": "Course Duration", "type": "cds.Decimal", "precision": 34, "scale": 1}, "TotalLearningHours": {"@EndUserText.label": "Total Hours", "type": "cds.Decimal", "precision": 34, "scale": 1}, "TotalCreditHours": {"@EndUserText.label": "Credit Hours", "type": "cds.Decimal", "precision": 34, "scale": 1}, "TotalCpeHours": {"@EndUserText.label": "CPE Hours", "type": "cds.Decimal", "precision": 34, "scale": 1}, "TotalTrainingHours": {"@EndUserText.label": "Total Training Hours", "type": "cds.Decimal", "precision": 34, "scale": 1}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "LearningEventHistory", "__namespace": "sap.bdc.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}