{"definitions": {"PerformanceRatingStd": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:PerformanceRatingStd", "@EntityRelationship.entityIds": [{"name": "PerformanceRatingStd", "propertyTypes": ["sap.sf.analytics:PerformanceRatingStdScaleId"]}], "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_DIMENSION"}, "@ObjectModel.supportedCapabilities": [{"#": "ANALYTICAL_DIMENSION"}], "elements": {"scaleId": {"@EndUserText.label": "Feedback ID", "key": true, "type": "cds.Integer64", "notNull": true}, "rating": {"@EndUserText.label": "Rating", "type": "cds.Decimal", "precision": 34, "scale": 1}, "category": {"@EndUserText.label": "Category", "type": "cds.String"}, "label": {"@EndUserText.label": "labEl  ", "type": "cds.String"}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "PerformanceRatingStd", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}