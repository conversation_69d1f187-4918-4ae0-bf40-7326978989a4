{"definitions": {"PositionFact": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:PositionFact", "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_FACT"}, "@ObjectModel.supportedCapabilities": [{"#": "CDS_MODELING_DATA_SOURCE"}, {"#": "ANALYTICAL_PROVIDER"}], "elements": {"positionId": {"@EndUserText.label": "position Id", "key": true, "type": "cds.Integer64", "notNull": true}, "incumbentId": {"@EndUserText.label": "Incumbent Id", "type": "cds.String"}, "positionCode": {"@EndUserText.label": "Position Code", "key": true, "type": "cds.String", "notNull": true}, "companyId": {"@EndUserText.label": "Company Id", "type": "cds.Integer64"}, "businessUnitId": {"@EndUserText.label": "Business Unit Id", "type": "cds.Integer64"}, "divisionId": {"@EndUserText.label": "Division Id", "type": "cds.Integer64"}, "departmentId": {"@EndUserText.label": "Position Id", "type": "cds.Integer64"}, "locationId": {"@EndUserText.label": "Location Id", "type": "cds.Integer64"}, "costCenterId": {"@EndUserText.label": "Cost Center Id", "type": "cds.Integer64"}, "jobCodeId": {"@EndUserText.label": "Job Code Id", "type": "cds.Integer64"}, "jobTitleId": {"@EndUserText.label": "Job Title Id", "type": "cds.Integer64"}, "payGradeId": {"@EndUserText.label": "Pay Grade Id", "type": "cds.Integer64"}, "payRangeId": {"@EndUserText.label": "Pay Range Id", "type": "cds.Integer64"}, "levelId": {"@EndUserText.label": "Level Id", "type": "cds.Integer64"}, "employeeClassId": {"@EndUserText.label": "Employee Class Id", "type": "cds.Date"}, "positionCriticalityId": {"@EndUserText.label": "Position Criticality Id", "type": "cds.String"}, "effectiveStartDt": {"@EndUserText.label": "Start Date", "key": true, "type": "cds.Date", "notNull": true}, "effectveEndDt": {"@EndUserText.label": "End Date", "type": "cds.Date"}, "isEffectiveStatus": {"@EndUserText.label": "Is Effective Status", "type": "cds.String"}, "isRegularTemporary": {"@EndUserText.label": "Is Regular Temporary", "type": "cds.Integer64"}, "positionFte": {"@EndUserText.label": "Position FTE", "type": "cds.Decimal", "precision": 34, "scale": 17}, "isPositionVacant": {"@EndUserText.label": "Is Position Vacant", "type": "cds.<PERSON><PERSON><PERSON>"}, "eopPosition": {"@EndUserText.label": "End Of Period Position", "type": "cds.Double"}, "isApplicableSuccessionPosition": {"@EndUserText.label": "Is Applicable Succession Position", "type": "cds.Integer"}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "PositionFact", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}