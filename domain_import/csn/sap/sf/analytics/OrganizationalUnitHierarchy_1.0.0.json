{"definitions": {"OrganizationalUnitHierarchy": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:OrganizationalUnitHierarchy", "@EntityRelationship.entityIds": [{"name": "OrganizationalUnitHierarchy", "propertyTypes": ["sap.sf.analytics:OrganizationalUnitHierarchyOrgUnitId"]}], "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_DIMENSION"}, "@ObjectModel.supportedCapabilities": [{"#": "ANALYTICAL_DIMENSION"}], "elements": {"orgUnitId": {"@EndUserText.label": "Organization Unit id", "@EntityRelationship.propertyType": "sap.sf.analytics:OrganizationalUnitHierarchyOrgUnitId", "key": true, "type": "cds.String", "length": 256, "notNull": true}, "orgUnitLvl1": {"@EndUserText.label": "Organization Unit Level 1", "type": "cds.Integer64"}, "orgUnitLvl2": {"@EndUserText.label": "Organization Unit Level 2", "type": "cds.Integer64"}, "orgUnitLvl3": {"@EndUserText.label": "Organization Unit Level 3", "type": "cds.Integer64"}, "orgUnitLvl1Name": {"@EndUserText.label": "Organization Unit Name Level 1", "type": "cds.String"}, "orgUnitLvl2Name": {"@EndUserText.label": "Organization Unit Name Level 2", "type": "cds.String"}, "orgUnitLvl3Name": {"@EndUserText.label": "Organization Unit Name Level 3", "type": "cds.String"}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "OrganizationalUnitHierarchy", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}