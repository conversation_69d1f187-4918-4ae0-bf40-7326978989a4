{"definitions": {"EventReasonsAndCategory": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:EventReasonsAndCategory", "@EntityRelationship.entityIds": [{"name": "EventReasonsAndCategory", "propertyTypes": ["sap.sf.analytics:EventReasonsAndCategoryEventReasonCode"]}], "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_DIMENSION"}, "@ObjectModel.supportedCapabilities": [{"#": "ANALYTICAL_DIMENSION"}], "elements": {"eventId": {"@EndUserText.label": "Event Id", "type": "cds.Integer64"}, "eventIdCode": {"@EndUserText.label": "Id", "type": "cds.String", "length": 128}, "eventReasonCode": {"@EndUserText.label": "Event Reason ID", "@EntityRelationship.propertyType": "sap.sf.analytics:EventReasonsAndCategoryEventReasonCode", "key": true, "type": "cds.String", "length": 32, "notNull": true}, "eventReasonName": {"@EndUserText.label": "Event Reason Name", "type": "cds.String", "length": 32}, "eventReasonMainCat": {"@EndUserText.label": "Event Reason Main Name", "type": "cds.String", "length": 128}, "eventReasonSubCat": {"@EndUserText.label": "Event Reason Sub Category Name", "type": "cds.String", "length": 128}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "EventReasonsAndCategory", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}