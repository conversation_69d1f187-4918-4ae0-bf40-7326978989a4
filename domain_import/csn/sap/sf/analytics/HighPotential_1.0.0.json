{"definitions": {"HighPotential": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:HighPotential", "@EntityRelationship.entityIds": [{"name": "HighPotential", "propertyTypes": ["sap.sf.analytics:HighPotentialRatingScaleId"]}], "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_DIMENSION"}, "@ObjectModel.supportedCapabilities": [{"#": "ANALYTICAL_DIMENSION"}], "elements": {"ratingScaleId": {"@EndUserText.label": "Rating Scale Id", "key": true, "type": "cds.Integer64", "notNull": true}, "rating": {"@EndUserText.label": "Rating", "type": "cds.Decimal", "precision": 34, "scale": 1}, "label": {"@EndUserText.label": "label", "type": "cds.String"}, "category": {"@EndUserText.label": "Category", "type": "cds.String"}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "HighPotential", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}