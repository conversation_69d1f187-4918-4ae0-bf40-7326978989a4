{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/analytics/general/v1/metadata/DataProductConfig", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "DataProductConfig", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "definitions": {"DataProductConfig": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Data Product Configuration Schema", "@EntityRelationship.entityType": "sap.sf.analytics:DataProductConfig", "@EntityRelationship.entityIds": [{"name": "DataProductConfig", "propertyTypes": ["sap.sf.analytics:DataProductConfigDpConfigInternalId"]}], "elements": {"dataProductConfigId": {"type": "cds.String", "@EndUserText.label": "dataProductConfigId", "length": 255}, "name": {"type": "cds.String", "@EndUserText.label": "name", "length": 255}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 255}, "version": {"type": "cds.String", "@EndUserText.label": "Version", "length": 50}, "dpConfigInternalId": {"type": "cds.Integer64", "@EndUserText.label": "Internal Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.analytics:DataProductConfigDpConfigInternalId"}, "configParameters": {"type": "cds.Composition", "target": "DataProductConfig_configParameters", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "ConfigParameters", "on": [{"ref": ["configParameters", "DataProductConfig_dpConfigInternalId_virtual"]}, "=", {"ref": ["dpConfigInternalId"]}]}, "createdBy": {"type": "cds.String", "@EndUserText.label": "created<PERSON>y", "length": 255}, "createdDate": {"type": "cds.DateTime", "@EndUserText.label": "createdAt"}, "lastModifiedBy": {"type": "cds.String", "@EndUserText.label": "lastModifiedBy", "length": 255}, "lastModifiedDate": {"type": "cds.DateTime", "@EndUserText.label": "lastModifiedAt"}}}, "DataProductConfig_configParameters": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Config Parameters", "@EntityRelationship.entityType": "sap.sf.analytics:DataProductConfig_configParameters", "elements": {"DataProductConfig_dpConfigInternalId_virtual": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_DataProductConfig"}, "type": "cds.Integer64", "key": true}, "_DataProductConfig": {"type": "cds.Association", "target": "DataProductConfig", "cardinality": {"max": 1}, "on": [{"ref": ["_DataProductConfig", "dpConfigInternalId"]}, "=", {"ref": ["DataProductConfig_dpConfigInternalId_virtual"]}]}, "paramValueInternalId": {"type": "cds.Integer64", "@EndUserText.label": "paramValueInternalId", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.analytics:DataProductConfig_configParametersParamValueInternalId"}, "configParamId": {"type": "cds.String", "@EndUserText.label": "config Param Id", "length": 255}, "moduleId": {"type": "cds.String", "@EndUserText.label": "Module Id", "length": 255}, "key": {"type": "cds.String", "@EndUserText.label": "Key of the value", "length": 255}, "value": {"type": "cds.String", "@EndUserText.label": "Config param value", "length": 255}}}}}