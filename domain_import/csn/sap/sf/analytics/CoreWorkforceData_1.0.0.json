{"definitions": {"CoreWorkforce_standardFields": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:CoreWorkforce_standardFields", "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_FACT"}, "@ObjectModel.supportedCapabilities": [{"#": "CDS_MODELING_DATA_SOURCE"}, {"#": "ANALYTICAL_PROVIDER"}], "elements": {"age": {"@EndUserText.label": "Age", "type": "cds.String", "length": 6}, "totalAgeCalc": {"@EndUserText.label": "Total Age Calc", "type": "cds.Integer"}, "avgHeadcount": {"@EndUserText.label": "Average Headcount", "type": "cds.Double"}, "annualSalary": {"@EndUserText.label": "Annual Salary", "type": "cds.Decimal", "precision": 16, "scale": 4}, "annualSalaryLocalCurr": {"@EndUserText.label": "Annual Salary Local Currency", "type": "cds.Decimal", "precision": 16, "scale": 4}, "businessUnit": {"@EndUserText.label": "Business Unit", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:businessUnit", "referencedPropertyType": "sap.sf.foundationobjects:businessUnitId"}], "type": "cds.Integer64"}, "company": {"@EndUserText.label": "Company", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:LegalEntity", "referencedPropertyType": "sap.sf.foundationobjects:legalEntityId"}], "type": "cds.Integer64"}, "contractType": {"@EndUserText.label": "Contract Type", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:contractcategory_fra", "referencedPropertyType": "sap.sf.extensibility:optionId"}], "type": "cds.Integer64"}, "costCenter": {"@EndUserText.label": "Cost Center", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:costCenter", "referencedPropertyType": "sap.sf.foundationobjects:costCenterId"}], "type": "cds.Integer64"}, "countryCode": {"@EndUserText.label": "Country Code", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:country", "referencedPropertyType": "sap.sf.foundationobjects:countryId"}], "type": "cds.Integer64"}, "criticalPosition": {"@EndUserText.label": "Critical Position", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:positionCriticality", "referencedPropertyType": "sap.sf.extensibility:optionId"}], "type": "cds.Integer64"}, "department": {"@EndUserText.label": "Department", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:department", "referencedPropertyType": "sap.sf.foundationobjects:departmentId"}], "type": "cds.Integer64"}, "division": {"@EndUserText.label": "Division", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:division", "referencedPropertyType": "sap.sf.foundationobjects:divisionId"}], "type": "cds.Integer64"}, "eeoClass": {"@EndUserText.label": "Equal Employment Opportunity Class", "type": "cds.Integer64"}, "eeoJobCategory": {"@EndUserText.label": "Equal Employment Opportunity Job Category", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:eeojobcategory_usa", "referencedPropertyType": "sap.sf.extensibility:optionId"}], "type": "cds.Integer64"}, "eeoJobGroupUSA": {"@EndUserText.label": "Equal Employment Opportunity Group USA", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:eeojobgroup_usa", "referencedPropertyType": "sap.sf.extensibility:optionId"}], "type": "cds.Integer64"}, "employeeClass": {"@EndUserText.label": "Employee Class", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:employeeclass", "referencedPropertyType": "sap.sf.extensibility:optionId"}], "type": "cds.Integer64"}, "employeeType": {"@EndUserText.label": "Employee Type", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:employee_type", "referencedPropertyType": "sap.sf.extensibility:optionId"}], "type": "cds.Integer64"}, "employmentType": {"@EndUserText.label": "Employment Type", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:employmenttype", "referencedPropertyType": "sap.sf.extensibility:optionId"}], "type": "cds.Integer64"}, "employmentStatus": {"@EndUserText.label": "Employment Status", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:jobstatus_chn", "referencedPropertyType": "sap.sf.extensibility:externalCode"}], "type": "cds.String", "length": 128}, "endDate": {"@EndUserText.label": "End Date", "type": "cds.Date"}, "eopFte": {"@EndUserText.label": "End of Period FTE", "type": "cds.Decimal", "precision": 34, "scale": 2}, "eopFteSalary": {"@EndUserText.label": "End of Period FTE Salary", "type": "cds.Decimal", "precision": 34, "scale": 2}, "eopHeadcount": {"@EndUserText.label": "End of Period Headcount", "type": "cds.Double"}, "eopHcSalary": {"@EndUserText.label": "End of Period Headcount Salary", "type": "cds.Double"}, "eventId": {"@EndUserText.label": "Event Id", "type": "cds.String", "length": 128}, "eventReasonCode": {"@EndUserText.label": "Event Reason Code", "type": "cds.String", "length": 32}, "externalHireFte": {"@EndUserText.label": "External Hire FTE", "type": "cds.Decimal", "precision": 34, "scale": 2}, "externalHires": {"@EndUserText.label": "External Hire", "type": "cds.Integer"}, "flsaStatus": {"@EndUserText.label": "Fair Labor Standards Act Status", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:flsastatus_usa", "referencedPropertyType": "sap.sf.extensibility:optionId"}], "type": "cds.Integer64"}, "fte": {"@EndUserText.label": "FTE", "type": "cds.Decimal", "precision": 34, "scale": 2}, "gender": {"@EndUserText.label": "Gender", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:gender_deu", "referencedPropertyType": "sap.sf.extensibility:externalCode"}], "type": "cds.String", "length": 2}, "generation": {"@EndUserText.label": "Workforce Generation", "type": "cds.String", "length": 32}, "tenureStartDate": {"@EndUserText.label": "Hire Date", "type": "cds.Date"}, "isContingentWorker": {"@EndUserText.label": "Is Contingent Worker", "type": "cds.<PERSON><PERSON><PERSON>"}, "isCrossBorderWorker": {"@EndUserText.label": "Is Cross Border Worker", "type": "cds.<PERSON><PERSON><PERSON>"}, "isEligibleForBenefit": {"@EndUserText.label": "Is Eligible For Benefit", "type": "cds.<PERSON><PERSON><PERSON>"}, "isEligibleForCar": {"@EndUserText.label": "Is Eligible For Car", "type": "cds.<PERSON><PERSON><PERSON>"}, "isFulltimeEmployee": {"@EndUserText.label": "Full-time or Part-time", "type": "cds.<PERSON><PERSON><PERSON>"}, "isHomeWorker": {"@EndUserText.label": "Is Home Worker", "type": "cds.<PERSON><PERSON><PERSON>"}, "primaryConcurrentEmployment": {"@EndUserText.label": "Is Primary Concurrent Employment", "type": "cds.<PERSON><PERSON><PERSON>"}, "isShiftEmployee": {"@EndUserText.label": "Is Shift Employee", "type": "cds.<PERSON><PERSON><PERSON>"}, "jobCode": {"@EndUserText.label": "Job Classification", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:jobClassification", "referencedPropertyType": "sap.sf.foundationobjects:jobClassificationId"}], "type": "cds.Integer64"}, "jobEntryDate": {"@EndUserText.label": "Job Entry Date", "type": "cds.Date"}, "jobDetailsId": {"@EndUserText.label": "Job Details Id", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.analytics:CoreWorkforce_CustomFields", "referencedPropertyType": "sap.sf.analytics:CoreWorkforce_CustomFieldsId"}], "type": "cds.Integer64"}, "jobTitle": {"@EndUserText.label": "Job Title", "type": "cds.String", "length": 256}, "location": {"@EndUserText.label": "Location", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.analytics:LocationHierarchy", "referencedPropertyType": "sap.sf.analytics:LocationHierarchyLocationId"}], "type": "cds.Integer64"}, "managerialEmployee": {"@EndUserText.label": "Managerial Employee", "type": "cds.<PERSON><PERSON><PERSON>"}, "managerId": {"@EndUserText.label": "Supervisor", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.analytics:SupervisorHierarchy", "referencedPropertyType": "sap.sf.analytics:SupervisorHierarchySupervisorChldNode"}], "type": "cds.String", "length": 256}, "maximumPay": {"@EndUserText.label": "Maximum Pay", "type": "cds.Decimal", "precision": 38, "scale": 2}, "midPoint": {"@EndUserText.label": "Mid Point", "type": "cds.Decimal", "precision": 38, "scale": 2}, "minimumPay": {"@EndUserText.label": "Minimum Pay", "type": "cds.Decimal", "precision": 38, "scale": 2}, "moveInSource": {"@EndUserText.label": "Move-In Source", "type": "cds.String", "length": 32}, "movementIn": {"@EndUserText.label": "Movement In", "type": "cds.Integer"}, "movementInFte": {"@EndUserText.label": "Movement In FTE", "type": "cds.Decimal", "precision": 34, "scale": 2}, "movementOut": {"@EndUserText.label": "Movement Out", "type": "cds.Integer"}, "movementOutFte": {"@EndUserText.label": "Movement Out FTE", "type": "cds.Decimal", "precision": 34, "scale": 2}, "moveOutReason": {"@EndUserText.label": "Move-Out Source", "type": "cds.String", "length": 32}, "directReports": {"@EndUserText.label": "Direct Reportees Count", "type": "cds.Integer"}, "organizationalTenure": {"@EndUserText.label": "Organizational Tenure", "type": "cds.String", "length": 6}, "orgUnitId": {"@EndUserText.label": "Organizational Unit", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.analytics:OrganizationalUnitHierarchy", "referencedPropertyType": "sap.sf.analytics:OrganizationalUnitHierarchyOrgUnitId"}], "type": "cds.String", "length": 256}, "payGrade": {"@EndUserText.label": "Salary Grade", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:payGrade", "referencedPropertyType": "sap.sf.foundationobjects:payGradeId"}], "type": "cds.Integer64"}, "payGroup": {"@EndUserText.label": "Pay Group", "type": "cds.Integer64"}, "payScaleArea": {"@EndUserText.label": "Pay Scale Area", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:payScaleArea", "referencedPropertyType": "sap.sf.foundationobjects:payScaleAreaId"}], "type": "cds.Integer64"}, "payScaleGroup": {"@EndUserText.label": "Pay Scale Group", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:payScaleGroup", "referencedPropertyType": "sap.sf.foundationobjects:payScaleGroupId"}], "type": "cds.Integer64"}, "payScaleLevel": {"@EndUserText.label": "Pay Scale Level", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:payScaleLevel", "referencedPropertyType": "sap.sf.foundationobjects:payScaleLevelId"}], "type": "cds.Integer64"}, "payScaleType": {"@EndUserText.label": "Pay Scale Type", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:payScaleType", "referencedPropertyType": "sap.sf.foundationobjects:payScaleTypeId"}], "type": "cds.Integer64"}, "perPersonOid": {"@EndUserText.label": "Per Person OID", "type": "cds.String", "length": 128}, "personUuid": {"@EndUserText.label": "Person UUID", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:person", "referencedPropertyType": "sap.sf.workforce:personId"}], "type": "cds.String", "length": 32}, "position": {"@EndUserText.label": "Position", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:position", "referencedPropertyType": "sap.sf.workforce:positionId"}], "type": "cds.Integer64"}, "positionEntryDate": {"@EndUserText.label": "Position Entry Date", "type": "cds.Date"}, "expectedFTE": {"@EndUserText.label": "Expected FTE", "type": "cds.Decimal", "precision": 34, "scale": 2}, "regularTemp": {"@EndUserText.label": "Regular or Temporary", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:regulartemporary", "referencedPropertyType": "sap.sf.extensibility:optionId"}], "type": "cds.Integer64"}, "salaryRange": {"@EndUserText.label": "Salary Range", "type": "cds.String", "length": 6}, "seqNumber": {"@EndUserText.label": "Sequence Number", "key": true, "type": "cds.Integer64", "notNull": true}, "sopHeadcount": {"@EndUserText.label": "Start of Period Headcount", "type": "cds.Double"}, "sourceCurrency": {"@EndUserText.label": "Source Currency", "type": "cds.String", "length": 32}, "standardWeeklyHours": {"@EndUserText.label": "Standard Weekly Hours", "type": "cds.Integer"}, "startDate": {"@EndUserText.label": "Start Date", "key": true, "type": "cds.Date", "notNull": true}, "supervisorLevel": {"@EndUserText.label": "Supervisor Level", "type": "cds.String", "length": 256}, "targetCurrency": {"@EndUserText.label": "Target Currency", "type": "cds.String", "length": 128}, "termBasedOnSalInteger": {"@EndUserText.label": "Termination Based on Salary", "type": "cds.Integer"}, "terminationFte": {"@EndUserText.label": "Terminations in FTE", "type": "cds.Decimal", "precision": 34, "scale": 2}, "positionTenure": {"@EndUserText.label": "Position Tenure", "type": "cds.String", "length": 6}, "terminations": {"@EndUserText.label": "Terminations", "type": "cds.Integer"}, "termSal": {"@EndUserText.label": "Termination Salary", "type": "cds.Decimal", "precision": 16, "scale": 4}, "totalPositionTenureCalc": {"@EndUserText.label": "Total Position Tenure Calc", "type": "cds.Integer"}, "totalRetAgeCalc": {"@EndUserText.label": "Total Retirement Age Calculation", "type": "cds.Integer"}, "totalOrgTenureCalc": {"@EndUserText.label": "Total Tenure Calculation", "type": "cds.Integer"}, "totPosProCalc": {"@EndUserText.label": "Total Position Prior to Promotion Calc", "type": "cds.Integer"}, "travelDistance": {"@EndUserText.label": "Home to Work Distance", "type": "cds.Decimal", "precision": 34, "scale": 3}, "userId": {"@EndUserText.label": "User ID", "key": true, "type": "cds.String", "length": 100, "notNull": true}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "CoreWorkforceData", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}