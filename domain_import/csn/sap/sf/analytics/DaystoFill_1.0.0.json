{"definitions": {"DaystoFill": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:DaystoFill", "@EntityRelationship.entityIds": [{"name": "DaystoFill", "propertyTypes": ["sap.sf.analytics:DaystoFillStartChldNode"]}], "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_FACT"}, "@ObjectModel.supportedCapabilities": [{"#": "CDS_MODELING_DATA_SOURCE"}, {"#": "ANALYTICAL_PROVIDER"}], "elements": {"ApplicationId": {"@EndUserText.label": "Application Id", "key": true, "type": "cds.Integer64", "notNull": true}, "JobRequisitionId": {"@EndUserText.label": "Job Requisition Id", "key": true, "type": "cds.Integer64", "notNull": true}, "ApplicantId": {"@EndUserText.label": "Applicant Id", "type": "cds.Integer64"}, "EffectiveStartDate": {"@EndUserText.label": "Effective Start Date", "type": "cds.Date"}, "EffectiveEndDate": {"@EndUserText.label": "Effective End Date", "type": "cds.Date"}, "TimeOpenId": {"@EndUserText.label": "Time Open Id", "type": "cds.String", "length": 20}, "DaysToFill": {"@EndUserText.label": "Days To Fill", "type": "cds.Integer"}, "DaysToStart": {"@EndUserText.label": "Days To Start", "type": "cds.Integer"}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "DaystoFill", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}