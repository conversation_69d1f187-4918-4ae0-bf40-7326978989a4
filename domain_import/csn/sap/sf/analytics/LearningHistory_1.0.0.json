{"definitions": {"LearningHistory": {"kind": "entity", "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_FACT"}, "@ObjectModel.supportedCapabilities": [{"#": "CDS_MODELING_DATA_SOURCE"}, {"#": "ANALYTICAL_PROVIDER"}], "elements": {"PersonGuid": {"@EndUserText.label": "Person ID", "type": "cds.String"}, "StudentID": {"@EndUserText.label": "User ID", "key": true, "type": "cds.String", "notNull": true}, "ItemID": {"@EndUserText.label": "Component ID", "type": "cds.String"}, "ItemTypeID": {"@EndUserText.label": "Course Type ID", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.learning:coursetype", "referencedPropertyType": "sap.sf.learning:id"}], "type": "cds.String"}, "RevisionDate": {"@EndUserText.label": "Revision Date", "type": "cds.DateTime"}, "LearningItemID": {"@EndUserText.label": "Learning Entity ID", "key": true, "type": "cds.String", "notNull": true}, "LearningItemType": {"@EndUserText.label": "Learning Entity Type", "type": "cds.String"}, "CourseID": {"@EndUserText.label": "Course ID", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.learning:course", "referencedPropertyType": "sap.sf.learning:learningItemID"}], "type": "cds.String"}, "ProgramID": {"@EndUserText.label": "Program ID", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.learning:program", "referencedPropertyType": "sap.sf.learning:id"}], "type": "cds.String"}, "ItemCompletionStatusID": {"@EndUserText.label": "Completion Status", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.learning:completionstatus", "referencedPropertyType": "sap.sf.learning:id"}], "key": true, "type": "cds.String", "notNull": true}, "CompletionDateTime": {"@EndUserText.label": "Completion Date", "key": true, "type": "cds.DateTime", "notNull": true}, "ItemDuration": {"@EndUserText.label": "Course Duration", "type": "cds.Decimal", "precision": 34, "scale": 1}, "TotalLearningHours": {"@EndUserText.label": "Total Hours", "type": "cds.Decimal", "precision": 34, "scale": 1}, "TotalCreditHours": {"@EndUserText.label": "Credit Hours", "type": "cds.Decimal", "precision": 34, "scale": 1}, "TotalCpeHours": {"@EndUserText.label": "CPE Hours", "type": "cds.Decimal", "precision": 34, "scale": 1}, "TotalTrainingHours": {"@EndUserText.label": "Total Training Hours", "type": "cds.Decimal", "precision": 34, "scale": 1}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "LearningHistory", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}