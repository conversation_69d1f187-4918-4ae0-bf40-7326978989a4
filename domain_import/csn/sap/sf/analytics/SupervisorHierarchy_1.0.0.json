{"definitions": {"SupervisorHierarchy": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:SupervisorHierarchy", "@EntityRelationship.entityIds": [{"name": "SupervisorHierarchy", "propertyTypes": ["sap.sf.analytics:SupervisorHierarchySupervisorChldNode"]}], "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_DIMENSION"}, "@ObjectModel.supportedCapabilities": [{"#": "ANALYTICAL_DIMENSION"}], "elements": {"supervisorChldNode": {"@EndUserText.label": "User ID", "@EntityRelationship.propertyType": "sap.sf.analytics:SupervisorHierarchySupervisorChldNode", "key": true, "type": "cds.String", "length": 100, "notNull": true}, "supervisorPrntNode": {"@EndUserText.label": "Supervisor", "type": "cds.String", "length": 256}, "supervisorName": {"@EndUserText.label": "Supervisor Name", "type": "cds.String", "length": 500}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "SupervisorHierarchy", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}