{"definitions": {"PerformanceData": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:PerformanceData", "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_FACT"}, "@ObjectModel.supportedCapabilities": [{"#": "CDS_MODELING_DATA_SOURCE"}, {"#": "ANALYTICAL_PROVIDER"}], "elements": {"feedbackId": {"@EndUserText.label": "Feedback ID", "key": true, "type": "cds.Integer64", "notNull": true}, "userId": {"@EndUserText.label": "User ID", "type": "cds.String"}, "formId": {"@EndUserText.label": "Form ID", "type": "cds.Integer64"}, "formTemplateId": {"@EndUserText.label": "Form Template", "type": "cds.Integer64"}, "performanceFormStatusId": {"@EndUserText.label": "Form Status", "type": "cds.Integer64"}, "reviewDifferenceId": {"@EndUserText.label": "Review Difference", "type": "cds.Integer64"}, "currentPerformanceRating": {"@EndUserText.label": "Current Performance Rating", "type": "cds.Decimal", "precision": 34, "scale": 1}, "currentPerformanceRatingScaleId": {"@EndUserText.label": "Current Performance Rating Scale", "type": "cds.Integer64"}, "currentPerformanceRatingLabel": {"@EndUserText.label": "Current Performance Rating Label", "type": "cds.String"}, "previousReviewRating": {"@EndUserText.label": "Previous Review Rating", "type": "cds.Decimal", "precision": 34, "scale": 1}, "previousReviewRatingScaleId": {"@EndUserText.label": "Previous Review Rating Scale ID", "type": "cds.Integer64"}, "reviewPeriodStartDt": {"@EndUserText.label": "Review Period Start Date", "type": "cds.Date"}, "reviewPeriodEndDt": {"@EndUserText.label": "Review Period End Date", "type": "cds.Date"}, "normalizedCurrentPerformanceRating": {"@EndUserText.label": "Normalized Current Performance Rating", "type": "cds.Decimal", "precision": 34, "scale": 1}, "normalizedPreviousPerformanceRating": {"@EndUserText.label": "Normalized Previous Performance Rating", "type": "cds.Decimal", "precision": 34, "scale": 1}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "PerformanceData", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}