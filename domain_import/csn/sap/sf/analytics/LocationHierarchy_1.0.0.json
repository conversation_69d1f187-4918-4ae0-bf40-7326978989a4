{"definitions": {"LocationHierarchy": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:LocationHierarchy", "@EntityRelationship.entityIds": [{"name": "LocationHierarchy", "propertyTypes": ["sap.sf.analytics:LocationHierarchyLocationId"]}], "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_DIMENSION"}, "@ObjectModel.supportedCapabilities": [{"#": "ANALYTICAL_DIMENSION"}], "elements": {"locationId": {"@EndUserText.label": "Id", "@EntityRelationship.propertyType": "sap.sf.analytics:LocationHierarchyLocationId", "key": true, "type": "cds.Integer64", "notNull": true}, "locationLvl1": {"@EndUserText.label": "Country Id", "type": "cds.String", "length": 256}, "locationLvl2": {"@EndUserText.label": "State Id", "type": "cds.String", "length": 256}, "locationLvl3": {"@EndUserText.label": "City Id", "type": "cds.String", "length": 256}, "locationLvl4": {"@EndUserText.label": "Id", "type": "cds.Integer64"}, "locationLvl1Name": {"@EndUserText.label": "Country", "type": "cds.String", "length": 256}, "locationLvl2Name": {"@EndUserText.label": "State", "type": "cds.String", "length": 256}, "locationLvl3Name": {"@EndUserText.label": "City", "type": "cds.String", "length": 256}, "locationLvl4Name": {"@EndUserText.label": "Name", "type": "cds.String", "length": 32}, "latitude": {"@EndUserText.label": "GeoMap Latitude", "type": "cds.Decimal", "precision": 38, "scale": 19}, "longitude": {"@EndUserText.label": "GeoMap Longitude", "type": "cds.Decimal", "precision": 38, "scale": 19}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "LocationHierarchy", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}