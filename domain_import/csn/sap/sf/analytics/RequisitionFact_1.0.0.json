{"definitions": {"RequisitionFact": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:RequisitionFact", "@EntityRelationship.entityIds": [{"name": "RequisitionFact", "propertyTypes": ["sap.sf.analytics:RequisitionFactChldNode"]}], "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_DIMENSION"}, "@ObjectModel.supportedCapabilities": [{"#": "ANALYTICAL_DIMENSION"}], "elements": {"JobRequisitionId": {"@EndUserText.label": "JobRequisition Id", "key": true, "type": "cds.Integer64", "notNull": true}, "RequisitionTemplateId": {"@EndUserText.label": "Requisition Template Id", "type": "cds.Integer64", "notNull": true}, "ReqStatusId": {"@EndUserText.label": "Requisition Status Id", "key": true, "type": "cds.Integer64", "notNull": true}, "ReqSystemStatusId": {"@EndUserText.label": "Req System Status Id", "type": "cds.Integer64"}, "ReqCurrentStatusId": {"@EndUserText.label": "Req Current Status Id", "type": "cds.Integer64"}, "RecruiterId": {"@EndUserText.label": "Recruit<PERSON> Id", "type": "cds.String", "length": 100}, "jobCodeId": {"@EndUserText.label": "Job Code Id", "type": "cds.String", "length": 100}, "departmentId": {"@EndUserText.label": "Department Id", "type": "cds.String", "length": 100}, "divisionId": {"@EndUserText.label": " Division Id", "type": "cds.String", "length": 100}, "locationId": {"@EndUserText.label": "Location Id", "type": "cds.String", "length": 100}, "positionId": {"@EndUserText.label": "Position Id", "type": "cds.String", "length": 100}, "eeoGroup": {"@EndUserText.label": "eeo Group", "type": "cds.String", "length": 100}, "eeoJobCat": {"@EndUserText.label": "eeo Job Category", "type": "cds.String", "length": 100}, "costCenterId": {"@EndUserText.label": "Cost Center Id", "type": "cds.String", "length": 100}, "businessUnitId": {"@EndUserText.label": "Business Unit Id", "type": "cds.String", "length": 100}, "RequisitionStartDate": {"@EndUserText.label": "RequisitionStartDate", "type": "cds.Date"}, "RequisitionEndDate": {"@EndUserText.label": "Requisition End Date", "type": "cds.Date"}, "effectiveStartDate": {"@EndUserText.label": "Effective Start Date", "type": "cds.Date"}, "effectiveEndDate": {"@EndUserText.label": "effective End Date", "type": "cds.Date"}, "TotRecruiters": {"@EndUserText.label": "Total Recruiters", "type": "cds.String", "length": 100}, "TotReqs": {"@EndUserText.label": "Total Requisition", "type": "cds.Integer64"}, "ReqOpen": {"@EndUserText.label": "Requisition Open", "type": "cds.Integer"}, "ReqClose": {"@EndUserText.label": "Requisition Close", "type": "cds.Integer"}, "PosOpen": {"@EndUserText.label": "Position Open", "type": "cds.Decimal", "precision": 34, "scale": 2}, "PosClose": {"@EndUserText.label": "Position Close", "type": "cds.Integer"}, "eopRequisition": {"@EndUserText.label": "eop Requisition", "type": "cds.Integer"}, "eopPosition": {"@EndUserText.label": "eop Position", "type": "cds.Integer"}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "RequisitionFact", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}