{"definitions": {"Application": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:Application", "@EntityRelationship.entityIds": [{"name": "Application", "propertyTypes": ["sap.sf.analytics:ApplicationChldNode"]}], "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_FACT"}, "@ObjectModel.supportedCapabilities": [{"#": "CDS_MODELING_DATA_SOURCE"}, {"#": "ANALYTICAL_PROVIDER"}], "elements": {"ApplicationId": {"@EndUserText.label": "Application Id", "key": true, "type": "cds.Integer64", "notNull": true}, "JobRequisitionId": {"@EndUserText.label": "Job Requisition Id", "key": true, "type": "cds.Integer64", "notNull": true}, "ApplicantId": {"@EndUserText.label": "Applicant Id", "type": "cds.Integer64"}, "ApplicationStatusId": {"@EndUserText.label": "Application Status Id", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.analytics:ApplicationStatus", "referencedPropertyType": "sap.sf.analytics:ApplicationStatusChldNode"}], "type": "cds.Integer64"}, "ApplicantSourceId": {"@EndUserText.label": "Applicant Source Id", "type": "cds.Integer64"}, "CandidateTypeId": {"@EndUserText.label": "Candidate Type Id", "type": "cds.Integer64"}, "ApplicationRevisionNumber": {"@EndUserText.label": "Application Revision Number", "key": true, "type": "cds.Integer64"}, "EffectiveStartDate": {"@EndUserText.label": "Effective Start Date", "type": "cds.Date"}, "EffectiveEndDate": {"@EndUserText.label": "Effective End Date", "type": "cds.Date"}, "ApplicationStartDate": {"@EndUserText.label": "Application Start Date", "type": "cds.Date"}, "ApplicationEndDate": {"@EndUserText.label": "Application End Date", "type": "cds.Date"}, "HireDate": {"@EndUserText.label": "Hire Date", "type": "cds.Date"}, "TotApplication": {"@EndUserText.label": "Total Application", "type": "cds.Integer64"}, "Applicants": {"@EndUserText.label": "Applicants", "type": "cds.Integer64"}, "ApplicationWithClosedReq": {"@EndUserText.label": "Application With Closed Requisition", "type": "cds.Integer64"}, "AppEvents": {"@EndUserText.label": "Application Events", "type": "cds.Integer64"}, "TotalHires": {"@EndUserText.label": "Total Hires", "type": "cds.Integer64"}, "InternalHires": {"@EndUserText.label": "Internal Hires", "type": "cds.Integer64"}, "ExternalHires": {"@EndUserText.label": "External Hires", "type": "cds.Integer64"}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "Application", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}