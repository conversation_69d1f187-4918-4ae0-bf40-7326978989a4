{"definitions": {"WorkforceSkillsData": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:WorkforceSkillsData", "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_FACT"}, "@ObjectModel.supportedCapabilities": [{"#": "CDS_MODELING_DATA_SOURCE"}, {"#": "ANALYTICAL_PROVIDER"}], "elements": {"startDate": {"@EndUserText.label": "Skill Start Date", "key": true, "type": "cds.Date", "notNull": true}, "endDate": {"@EndUserText.label": "Skill End Date", "type": "cds.Date"}, "growthPortfolioId": {"@EndUserText.label": "Growth Portfolio Id", "key": true, "type": "cds.Integer64", "notNull": true}, "personUuid": {"@EndUserText.label": "Person UUID", "type": "cds.String", "length": 256}, "libraryId": {"@EndUserText.label": "Skill", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.capabilities:library", "referencedPropertyType": "sap.sf.capabilities:libraryLibraryId"}], "type": "cds.Integer64"}, "libraryStatus": {"@EndUserText.label": "Skill Status", "type": "cds.String", "length": 50}, "libraryTypeId": {"@EndUserText.label": "Skill Type", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.capabilities:libraryType", "referencedPropertyType": "sap.sf.capabilities:libraryTypeLibraryTypeId"}], "type": "cds.Integer64"}, "proficiencySetId": {"@EndUserText.label": "Proficiency Set", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.capabilities:proficiencySet", "referencedPropertyType": "sap.sf.capabilities:proficiencySetProficiencySetId"}], "type": "cds.Integer64"}, "proficiencyLevelId": {"@EndUserText.label": "Proficiency Level", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.capabilities:proficiencySet_proficiencyLevels", "referencedPropertyType": "sap.sf.capabilities:proficiencySet_proficiencyLevelsProficiencyLevelId"}], "type": "cds.Integer64"}, "expectedProficiencyLevelId": {"@EndUserText.label": "Expected Proficiency Level", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.capabilities:proficiencySet_proficiencyLevels", "referencedPropertyType": "sap.sf.capabilities:proficiencySet_proficiencyLevelsProficiencyLevelId"}], "type": "cds.Integer64"}, "expectationAboveBelowPar": {"@EndUserText.label": "Proficiency Status", "type": "cds.String", "length": 10}, "changeInProficiencyValue": {"@EndUserText.label": "Change in Proficiency", "type": "cds.String", "length": 2}, "skillMaintained": {"@EndUserText.label": "Skill Profile Maintained", "type": "cds.String", "length": 3}, "proficiencyGap": {"@EndUserText.label": "Proficiency Gap", "type": "cds.Integer"}, "proficiencyLevel": {"@EndUserText.label": "Proficiency Rating", "type": "cds.Integer64"}, "expectedProficiencyLevel": {"@EndUserText.label": "Expected Proficiency Rating", "type": "cds.Integer64"}, "skillGain": {"@EndUserText.label": "<PERSON><PERSON>", "type": "cds.Integer"}, "skillLoss": {"@EndUserText.label": "Skill Loss", "type": "cds.Integer"}, "skillCounter": {"@EndUserText.label": "Skill Counter", "type": "cds.Integer"}, "proficiencyLevelObtained": {"@EndUserText.label": "Proficiency Level Obtained", "type": "cds.Integer"}, "skillProficiencyChanged": {"@EndUserText.label": "Skill Proficiency Changed", "type": "cds.Integer"}, "userId": {"@EndUserText.label": "User ID", "key": true, "type": "cds.String", "length": 100, "notNull": true}, "age": {"@EndUserText.label": "Age", "type": "cds.String", "length": 6}, "businessUnit": {"@EndUserText.label": "Business Unit", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:businessUnit", "referencedPropertyType": "sap.sf.foundationobjects:businessUnitId"}], "type": "cds.Integer64"}, "company": {"@EndUserText.label": "Company", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:legalEntity", "referencedPropertyType": "sap.sf.foundationobjects:legalEntityId"}], "type": "cds.Integer64"}, "contractType": {"@EndUserText.label": "Contract Type", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:contractcategory_fra", "referencedPropertyType": "sap.sf.extensibility:optionId"}], "type": "cds.Integer64"}, "costCenter": {"@EndUserText.label": "Cost Center", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:costCenter", "referencedPropertyType": "sap.sf.foundationobjects:costCenterId"}], "type": "cds.Integer64"}, "countryCode": {"@EndUserText.label": "Country Code", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:country", "referencedPropertyType": "sap.sf.foundationobjects:countryId"}], "type": "cds.Integer64"}, "criticalPosition": {"@EndUserText.label": "Critical Position", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:positionCriticality", "referencedPropertyType": "sap.sf.extensibility:optionId"}], "type": "cds.Integer64"}, "department": {"@EndUserText.label": "Department", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:department", "referencedPropertyType": "sap.sf.foundationobjects:departmentId"}], "type": "cds.Integer64"}, "division": {"@EndUserText.label": "Division", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:division", "referencedPropertyType": "sap.sf.foundationobjects:divisionId"}], "type": "cds.Integer64"}, "eeoClass": {"@EndUserText.label": "Equal Employment Opportunity Class", "type": "cds.Integer64"}, "eeoJobCategory": {"@EndUserText.label": "Equal Employment Opportunity Job Category", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:eeojobcategory_usa", "referencedPropertyType": "sap.sf.extensibility:optionId"}], "type": "cds.Integer64"}, "eeoJobGroupUSA": {"@EndUserText.label": "Equal Employment Opportunity Group USA", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:eeojobgroup_usa", "referencedPropertyType": "sap.sf.extensibility:optionId"}], "type": "cds.Integer64"}, "employeeClass": {"@EndUserText.label": "Employee Class", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:employeeclass", "referencedPropertyType": "sap.sf.extensibility:optionId"}], "type": "cds.Integer64"}, "employeeType": {"@EndUserText.label": "Employee Type", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:employee_type", "referencedPropertyType": "sap.sf.extensibility:optionId"}], "type": "cds.Integer64"}, "employmentType": {"@EndUserText.label": "Employment Type", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:employmenttype", "referencedPropertyType": "sap.sf.extensibility:optionId"}], "type": "cds.Integer64"}, "employmentStatus": {"@EndUserText.label": "Employment Status", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:jobstatus_chn", "referencedPropertyType": "sap.sf.extensibility:externalCode"}], "type": "cds.String", "length": 128}, "eventReasonCode": {"@EndUserText.label": "Event Reason Code", "type": "cds.String", "length": 32}, "flsaStatus": {"@EndUserText.label": "Fair Labor Standards Act Status", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:flsastatus_usa", "referencedPropertyType": "sap.sf.extensibility:optionId"}], "type": "cds.Integer64"}, "gender": {"@EndUserText.label": "Gender", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:gender_deu", "referencedPropertyType": "sap.sf.extensibility:externalCode"}], "type": "cds.String", "length": 2}, "generation": {"@EndUserText.label": "Workforce Generation", "type": "cds.String", "length": 32}, "isContingentWorker": {"@EndUserText.label": "Is Contingent Worker", "type": "cds.<PERSON><PERSON><PERSON>"}, "isFulltimeEmployee": {"@EndUserText.label": "Full-time or Part-time", "type": "cds.<PERSON><PERSON><PERSON>"}, "primaryConcurrentEmployment": {"@EndUserText.label": "Is Primary Concurrent Employment", "type": "cds.<PERSON><PERSON><PERSON>"}, "jobCode": {"@EndUserText.label": "Job Classification", "type": "cds.Integer64"}, "jobTitle": {"@EndUserText.label": "Job Title", "type": "cds.String", "length": 256}, "location": {"@EndUserText.label": "Location", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.analytics:locationhierarchy", "referencedPropertyType": "sap.sf.analytics:LocationHierarchyLocationId"}], "type": "cds.Integer64"}, "managerialEmployee": {"@EndUserText.label": "Managerial Employee", "type": "cds.<PERSON><PERSON><PERSON>"}, "managerId": {"@EndUserText.label": "Supervisor", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.analytics:supervisorhierarchy", "referencedPropertyType": "sap.sf.analytics:SupervisorHierarchySupervisorChldNode"}], "type": "cds.String", "length": 256}, "directReports": {"@EndUserText.label": "Direct Reportees Count", "type": "cds.Integer"}, "organizationalTenure": {"@EndUserText.label": "Organizational Tenure", "type": "cds.String", "length": 6}, "positionTenure": {"@EndUserText.label": "Position Tenure", "type": "cds.String", "length": 6}, "orgUnitId": {"@EndUserText.label": "Organizational Unit", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.analytics:organizationunit", "referencedPropertyType": "sap.sf.analytics:OrganizationalUnitHierarchyOrgUnitId"}], "type": "cds.String", "length": 256}, "payGrade": {"@EndUserText.label": "Salary Grade", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:payGrade", "referencedPropertyType": "sap.sf.foundationobjects:PayGradeId"}], "type": "cds.Integer64"}, "eopHeadcount": {"@EndUserText.label": "End of Period Headcount", "type": "cds.Double"}, "avgHeadcount": {"@EndUserText.label": "Average Headcount", "type": "cds.Double"}, "position": {"@EndUserText.label": "Position", "type": "cds.Integer64"}, "regularTemp": {"@EndUserText.label": "Regular or Temporary", "type": "cds.Integer64"}, "seqNumber": {"@EndUserText.label": "Sequence Number", "key": true, "type": "cds.Integer64", "notNull": true}, "terminations": {"@EndUserText.label": "Terminations", "type": "cds.Integer"}, "skillTerminations": {"@EndUserText.label": "Skill Terminations", "type": "cds.Integer"}, "skillMovementIn": {"@EndUserText.label": "Skill Movement In", "type": "cds.Integer"}, "skillMovementOut": {"@EndUserText.label": "Skilled Movement Out", "type": "cds.Integer"}, "skillExternalHires": {"@EndUserText.label": "Skill External Hires", "type": "cds.Integer"}, "totalPositionTenureCalc": {"@EndUserText.label": "Total Position Tenure Calc", "type": "cds.Integer"}, "moveInSource": {"@EndUserText.label": "Move-In Source", "type": "cds.String", "length": 32}, "moveOutReason": {"@EndUserText.label": "Move-Out Source", "type": "cds.String", "length": 32}, "jobDetailsId": {"@EndUserText.label": "Job Details Id", "@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.analytics:CoreWorkforce_CustomFields", "referencedPropertyType": "sap.sf.analytics:CoreWorkforce_CustomFieldsId"}], "type": "cds.Integer64"}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "WorkforceSkillsData", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}