{"definitions": {"DaystoFillStartFact": {"kind": "entity", "@EntityRelationship.entityType": "sap.sf.analytics:DaystoFillStartFact", "@EntityRelationship.entityIds": [{"name": "DaystoFillStartFact", "propertyTypes": ["sap.sf.analytics:DaystoFillStartChldNode"]}], "@ObjectModel.modelingPattern": {"#": "ANALYTICAL_DIMENSION"}, "@ObjectModel.supportedCapabilities": [{"#": "ANALYTICAL_DIMENSION"}], "elements": {"ApplicationId": {"@EndUserText.label": "Application Id", "key": true, "type": "cds.Integer64", "notNull": true}, "JobRequisitionId": {"@EndUserText.label": "Job Requisition Id", "key": true, "type": "cds.Integer64", "notNull": true}, "ApplicantId": {"@EndUserText.label": "Applicant Id", "type": "cds.Integer64"}, "EffectiveStartDate": {"@EndUserText.label": "Effective Start Date", "type": "cds.Date"}, "EffectiveEndDate": {"@EndUserText.label": "Effective End Date", "type": "cds.Date"}, "TimeOpenId": {"@EndUserText.label": "Time Open Id", "type": "cds.String", "length": 20}, "DaysToFill": {"@EndUserText.label": "Days To Fill", "type": "cds.Integer"}, "DaysToStart": {"@EndUserText.label": "Days To Start", "type": "cds.Integer"}}}}, "meta": {"creator": "CDS Compiler v5.8.2", "flavor": "inferred", "__name": "DaystoFillStartFact", "__namespace": "sap.sf.analytics", "document": {"version": "1.0.0"}}, "$version": "2.0", "csnInteropEffective": "1.0", "$id": "id"}