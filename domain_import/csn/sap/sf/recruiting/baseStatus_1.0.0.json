{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/recruiting/application/v1/metadata/baseStatus", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "BaseStatus", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.recruiting", "document": {"version": "1.0.0"}}, "definitions": {"BaseStatus": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Base Status", "@EntityRelationship.entityType": "sap.sf.recruiting:BaseStatus", "@EntityRelationship.entityIds": [{"name": "BaseStatus", "propertyTypes": ["sap.sf.recruiting:BaseStatusId"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Base Status Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:BaseStatusId"}, "name": {"type": "cds.String", "@EndUserText.label": "Status Name", "length": 100}, "ordinal": {"type": "cds.Integer64", "@EndUserText.label": "Status Order", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "type": {"type": "cds.String", "@EndUserText.label": "Status Type", "length": 100}, "category": {"type": "cds.String", "@EndUserText.label": "Status Category", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Application Status Last modified date"}, "modifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Application Status last modified by", "length": 4000}}}}}