{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/recruiting/requisition/v1/metadata/jobRequisitionPosting", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "JobRequisitionPosting", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.recruiting", "document": {"version": "1.0.0"}}, "definitions": {"JobRequisitionPosting": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Job Requisition Postings", "@EntityRelationship.entityType": "sap.sf.recruiting:JobRequisitionPosting", "@EntityRelationship.entityIds": [{"name": "JobRequisitionPosting", "propertyTypes": ["sap.sf.recruiting:JobRequisitionPostingId"]}], "elements": {"jobReqId": {"type": "cds.Integer64", "@EndUserText.label": "Job Req Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Job Posting Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:JobRequisitionPostingId"}, "boardId": {"type": "cds.String", "@EndUserText.label": "Job Board Id", "length": 4000}, "channelId": {"type": "cds.String", "@EndUserText.label": "Job Channel Id", "length": 100}, "boardName": {"type": "cds.String", "@EndUserText.label": "Job Board Name", "length": 100}, "postingStatus": {"type": "cds.String", "@EndUserText.label": "Job Posting Status", "length": 4000}, "postedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Job Posting By", "length": 4000, "@PersonalData.isPotentiallyPersonal": true}, "modifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Posting Last Modified By", "length": 4000, "@PersonalData.isPotentiallyPersonal": true}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Posting Last Modified Date"}, "postStartDate": {"type": "cds.DateTime", "@EndUserText.label": "Posting Start Date"}, "postEndDate": {"type": "cds.DateTime", "@EndUserText.label": "Posting End DateDate"}, "agencyComments": {"type": "cds.String", "@EndUserText.label": "Agency Comments", "length": 4000}, "extPartnerAccountId": {"type": "cds.Integer64", "@EndUserText.label": "Ext Partner Account Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}}}}}