{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/recruiting/application/v1/metadata/jobApplication", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "JobApplication", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.recruiting", "document": {"version": "1.0.0"}}, "definitions": {"JobApplication": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Job Application Details", "@EntityRelationship.entityType": "sap.sf.recruiting:JobApplication", "@EntityRelationship.entityIds": [{"name": "JobApplication", "propertyTypes": ["sap.sf.recruiting:JobApplicationId"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Job Application Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:JobApplicationId"}, "candidateId": {"type": "cds.Integer64", "@EndUserText.label": "Candidate Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@PersonalData.isPotentiallyPersonal": true}, "jobReqId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.recruiting:jobRequisition", "referencedPropertyType": "sap.sf.recruiting:jobRequisitionId"}], "type": "cds.Integer64", "@EndUserText.label": "Job requisition id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "status": {"type": "cds.Integer64", "@EndUserText.label": "Application Status", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "statusComment": {"type": "cds.String", "@EndUserText.label": "Status Comments", "length": 1000}, "hiredOn": {"type": "cds.DateTime", "@EndUserText.label": "Applicant Hired Date"}, "exportedOn": {"type": "cds.DateTime", "@EndUserText.label": "Applicant Exported Date"}, "snapshotAt": {"type": "cds.DateTime", "@EndUserText.label": "Snapshot Date"}, "country": {"type": "cds.String", "@EndUserText.label": "Country Code", "length": 1024}, "usersSysId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "User Sys Id", "length": 100, "@PersonalData.isPotentiallyPersonal": true}, "publicIntranet": {"type": "cds.String", "@EndUserText.label": "Public Intranet", "length": 1}, "guid": {"type": "cds.String", "@EndUserText.label": "Id for imported application", "length": 512}, "nonApplicantStatus": {"type": "cds.Integer64", "@EndUserText.label": "Non applicant status", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "urlHash": {"type": "cds.String", "@EndUserText.label": "<PERSON><PERSON><PERSON>", "length": 200}, "resumeUploadedAt": {"type": "cds.DateTime", "@EndUserText.label": "Resume uploaded date"}, "dataSource": {"type": "cds.Integer64", "@EndUserText.label": "Data Source", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "anonymized": {"type": "cds.String", "@EndUserText.label": "Anonymized Flag", "length": 1}, "anonymizedAt": {"type": "cds.DateTime", "@EndUserText.label": "Anonymized Date"}, "locale": {"type": "cds.String", "@EndUserText.label": "Application Locale", "length": 32}, "profileUpdated": {"type": "cds.String", "@EndUserText.label": "Is Profile Updated", "length": 1}, "duplicateProfile": {"type": "cds.String", "@EndUserText.label": "Duplicate Profile", "length": 1}, "modifiedOnBehalfOf": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Proxy User", "length": 100, "@PersonalData.isPotentiallyPersonal": true}, "owner": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Owner", "length": 100, "@PersonalData.isPotentiallyPersonal": true}, "candConversionProcessed": {"type": "cds.String", "@EndUserText.label": "Candidate Conversion Process"}, "candTypeWhenHired": {"type": "cds.String", "@EndUserText.label": "Candidate Type when hired", "length": 1}, "ownedFrom": {"type": "cds.DateTime", "@EndUserText.label": "Ownership date"}, "timeToHire": {"type": "cds.Decimal", "@EndUserText.label": "Time to hire", "precision": 18, "scale": 2}, "personId": {"type": "cds.Integer64", "@EndUserText.label": "Person id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "@PersonalData.isPotentiallyPersonal": true}, "segment": {"type": "cds.Integer64", "@EndUserText.label": "Segment id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "referredBy": {"type": "cds.String", "@EndUserText.label": "Referred By", "length": 100}, "modifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Last Modified by", "length": 100, "@PersonalData.isPotentiallyPersonal": true}, "source": {"type": "cds.Integer64", "@EndUserText.label": "Application Source", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "referralSource": {"type": "cds.Integer64", "@EndUserText.label": "Referral Source", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "ssn": {"type": "cds.String", "@EndUserText.label": "SSN", "length": 100, "@PersonalData.isPotentiallyPersonal": true}, "dateOfBirth": {"type": "cds.DateTime", "@EndUserText.label": "Date Of Birth", "@PersonalData.isPotentiallyPersonal": true}, "ethnicity": {"type": "cds.String", "@EndUserText.label": "Ethnicity", "length": 256}, "veteranStatus": {"type": "cds.String", "@EndUserText.label": "Veteran Status", "length": 256}, "disabilityStatus": {"type": "cds.String", "@EndUserText.label": "Disability Status", "length": 100}, "currentCompany": {"type": "cds.String", "@EndUserText.label": "Current Company", "length": 256}, "currentTitle": {"type": "cds.String", "@EndUserText.label": "Current Title", "length": 256}, "race": {"type": "cds.String", "@EndUserText.label": "Race", "length": 100}, "state": {"type": "cds.String", "@EndUserText.label": "State / Province", "length": 100}, "zip": {"type": "cds.String", "@EndUserText.label": "Zip / Postal Code", "length": 256}, "statusId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.recruiting:appStatus", "referencedPropertyType": "sap.sf.recruiting:appStatusId"}], "type": "cds.Integer64", "@EndUserText.label": "Application Status Set Item Id", "@Semantics.valueRange": {"minimum": "-9223372036854776000", "maximum": "9223372036854776000"}}, "lastName": {"type": "cds.String", "@EndUserText.label": "Applicant Last Name", "length": 128, "@PersonalData.isPotentiallyPersonal": true}, "gender": {"type": "cds.String", "@EndUserText.label": "Gender", "length": 2, "@PersonalData.isPotentiallySensitive": true}, "city": {"type": "cds.String", "@EndUserText.label": "City", "length": 100}, "address": {"type": "cds.String", "@EndUserText.label": "Address", "length": 256, "@PersonalData.isPotentiallyPersonal": true}, "cellPhone": {"type": "cds.String", "@EndUserText.label": "Cell Phone", "length": 100, "@PersonalData.isPotentiallyPersonal": true}, "homePhone": {"type": "cds.String", "@EndUserText.label": "Home Phone", "length": 100, "@PersonalData.isPotentiallyPersonal": true}, "firstName": {"type": "cds.String", "@EndUserText.label": "Applicant First Name", "length": 128, "@PersonalData.isPotentiallyPersonal": true}, "middleName": {"type": "cds.String", "@EndUserText.label": "Applicant Middle Name", "length": 128, "@PersonalData.isPotentiallyPersonal": true}, "questionResponse": {"type": "cds.Composition", "target": "QuestionResponse", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Question Response", "on": [{"ref": ["questionResponse", "_id"]}, "=", {"ref": ["id"]}]}, "comments": {"type": "cds.Composition", "target": "Comment", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Application Comments", "on": [{"ref": ["comments", "_id"]}, "=", {"ref": ["id"]}]}, "assessments": {"type": "cds.Composition", "target": "Assessment", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Application Assessment", "on": [{"ref": ["assessments", "_id"]}, "=", {"ref": ["id"]}]}, "onboardingInformation": {"type": "cds.Composition", "target": "OnboardingInformation", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Onboarding Information", "on": [{"ref": ["onboardingInformation", "_id"]}, "=", {"ref": ["id"]}]}, "backgroundChecks": {"type": "cds.Composition", "target": "<PERSON><PERSON><PERSON><PERSON>", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Candidate Background Check", "on": [{"ref": ["backgroundChecks", "_id"]}, "=", {"ref": ["id"]}]}, "statusAudits": {"type": "cds.Composition", "target": "StatusAudit", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Application Status Audit", "on": [{"ref": ["statusAudits", "_id"]}, "=", {"ref": ["id"]}]}, "skillProfile": {"type": "cds.Composition", "target": "SkillProfile", "cardinality": {"max": 1}, "@EndUserText.label": "Skill Profile", "on": [{"ref": ["skillProfile", "_id"]}, "=", {"ref": ["id"]}]}}}, "QuestionResponse": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Job Application Question Response", "@EntityRelationship.entityType": "sap.sf.recruiting:QuestionResponse", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_JobApplication"}, "type": "cds.Integer64", "key": true}, "_JobApplication": {"type": "cds.Association", "target": "JobApplication", "cardinality": {"max": 1}, "on": [{"ref": ["_JobApplication", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:QuestionResponseId"}, "question": {"type": "cds.String", "@EndUserText.label": "question", "length": 100}, "question-type": {"type": "cds.String", "@EndUserText.label": "question-type", "length": 100}, "expected-answer": {"type": "cds.String", "@EndUserText.label": "expected-answer", "length": 100}, "high-low": {"type": "cds.String", "@EndUserText.label": "high-low", "length": 100}, "answer": {"type": "cds.String", "@EndUserText.label": "answer", "length": 100}}}, "Comment": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Job Application Comments", "@EntityRelationship.entityType": "sap.sf.recruiting:Comment", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_JobApplication"}, "type": "cds.Integer64", "key": true}, "_JobApplication": {"type": "cds.Association", "target": "JobApplication", "cardinality": {"max": 1}, "on": [{"ref": ["_JobApplication", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Comment ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:CommentId"}, "commentator": {"type": "cds.String", "@EndUserText.label": "Commentator", "length": 100, "@PersonalData.isPotentiallyPersonal": true}, "content": {"type": "cds.String", "@EndUserText.label": "Content", "length": 4000}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100, "@PersonalData.isPotentiallyPersonal": true}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created Date"}, "modifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100, "@PersonalData.isPotentiallyPersonal": true}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}}}, "Assessment": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Job Application Assessment", "@EntityRelationship.entityType": "sap.sf.recruiting:Assessment", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_JobApplication"}, "type": "cds.Integer64", "key": true}, "_JobApplication": {"type": "cds.Association", "target": "JobApplication", "cardinality": {"max": 1}, "on": [{"ref": ["_JobApplication", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Assessment Order ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:AssessmentId"}, "associationId": {"type": "cds.Integer64", "@EndUserText.label": "Assessment Association ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Assessment Order Created By", "length": 45, "@PersonalData.isPotentiallyPersonal": true}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Assessment Order Creation Date"}, "modifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Assessment Order Last Modified By", "length": 45, "@PersonalData.isPotentiallyPersonal": true}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Assessment Order Last Modification Date"}, "orderStatus": {"type": "cds.Integer64", "@EndUserText.label": "Assessment Order Status", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "orderStatusDate": {"type": "cds.DateTime", "@EndUserText.label": "Assessment Order Status Date"}, "vendor": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.recruiting:assessmentPackage", "referencedPropertyType": "sap.sf.recruiting:assessmentPackageCode"}], "type": "cds.String", "@EndUserText.label": "Assessment Vendor", "length": 256}, "reports": {"type": "cds.Composition", "target": "Report", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Assessment Reports", "on": [{"ref": ["reports", "_id"]}, "=", {"ref": ["id"]}]}}}, "OnboardingInformation": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Job Application Onboarding Information", "@EntityRelationship.entityType": "sap.sf.recruiting:OnboardingInformation", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_JobApplication"}, "type": "cds.Integer64", "key": true}, "_JobApplication": {"type": "cds.Association", "target": "JobApplication", "cardinality": {"max": 1}, "on": [{"ref": ["_JobApplication", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Onboarding ID", "@Semantics.valueRange": {"minimum": "-9223372036854776000", "maximum": "9223372036854776000"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:OnboardingInformationId"}, "vendorApplicantId": {"type": "cds.String", "@EndUserText.label": "Vendor Applicant ID", "length": 50, "@PersonalData.isPotentiallyPersonal": true}, "orderNo": {"type": "cds.String", "@EndUserText.label": "Order No", "length": 50}, "vendor": {"type": "cds.String", "@EndUserText.label": "<PERSON><PERSON><PERSON>", "length": 50}, "accountId": {"type": "cds.String", "@EndUserText.label": "Account ID", "length": 25, "@PersonalData.isPotentiallyPersonal": true}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Onboarding creation done by", "length": 100, "@PersonalData.isPotentiallyPersonal": true}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Onboarding creation date"}, "version": {"type": "cds.Decimal", "@EndUserText.label": "Onboarding Version", "precision": 4, "scale": 1}, "statusId": {"type": "cds.Integer64", "@EndUserText.label": "Onboarding Status ID", "@Semantics.valueRange": {"minimum": "-9223372036854776000", "maximum": "9223372036854776000"}}, "type": {"type": "cds.String", "@EndUserText.label": "Onboarding Type", "length": 256}, "name": {"type": "cds.String", "@EndUserText.label": "Onboarding Name", "length": 256}, "statusType": {"type": "cds.String", "@EndUserText.label": "Onboarding Status Type", "length": 256}, "status": {"type": "cds.String", "@EndUserText.label": "Onboarding Status", "length": 256}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}}}, "BackgroundCheck": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Job Application Candidate Background Check", "@EntityRelationship.entityType": "sap.sf.recruiting:<PERSON><PERSON><PERSON><PERSON>", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_JobApplication"}, "type": "cds.Integer64", "key": true}, "_JobApplication": {"type": "cds.Association", "target": "JobApplication", "cardinality": {"max": 1}, "on": [{"ref": ["_JobApplication", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Background Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:Background<PERSON>heckId"}, "orderNo": {"type": "cds.String", "@EndUserText.label": "Order No", "length": 50}, "vendor": {"type": "cds.String", "@EndUserText.label": "<PERSON><PERSON><PERSON>", "length": 50}, "accountId": {"type": "cds.String", "@EndUserText.label": "Account Id", "length": 50, "@PersonalData.isPotentiallyPersonal": true}, "packageId": {"type": "cds.String", "@EndUserText.label": "Package Id", "length": 50}, "costCenterId": {"type": "cds.String", "@EndUserText.label": "Cost Center ID", "length": 50}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100, "@PersonalData.isPotentiallyPersonal": true}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created Date"}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "statusDetails": {"type": "cds.Composition", "target": "StatusDetail", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Status Details", "on": [{"ref": ["statusDetails", "_id"]}, "=", {"ref": ["id"]}]}}}, "StatusAudit": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Job Application Status Audit", "@EntityRelationship.entityType": "sap.sf.recruiting:<PERSON><PERSON><PERSON><PERSON>", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_JobApplication"}, "type": "cds.Integer64", "key": true}, "_JobApplication": {"type": "cds.Association", "target": "JobApplication", "cardinality": {"max": 1}, "on": [{"ref": ["_JobApplication", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Revision Number", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:StatusAuditId"}, "type": {"type": "cds.Integer64", "@EndUserText.label": "Revision Type", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "statusId": {"type": "cds.Integer64", "@EndUserText.label": "Application Status Set Item Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created Date"}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100, "@PersonalData.isPotentiallyPersonal": true}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "modifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100, "@PersonalData.isPotentiallyPersonal": true}, "skipped": {"type": "cds.Integer64", "@EndUserText.label": "Skipped Status", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}}}, "SkillProfile": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Job Application Skill Profile", "@EntityRelationship.entityType": "sap.sf.recruiting:SkillProfile", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_JobApplication"}, "type": "cds.Integer64", "key": true}, "_JobApplication": {"type": "cds.Association", "target": "JobApplication", "cardinality": {"max": 1}, "on": [{"ref": ["_JobApplication", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Skill Profile Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:SkillProfileId"}, "score": {"type": "cds.Decimal", "@EndUserText.label": "Overall Skill Score", "precision": 34, "scale": 3}, "industry": {"type": "cds.String", "@EndUserText.label": "Industry Name", "length": 200}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "validated": {"type": "cds.String", "@EndUserText.label": "Validated", "length": 1}, "skills": {"type": "cds.Composition", "target": "Skill", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "<PERSON><PERSON> Details", "on": [{"ref": ["skills", "_id"]}, "=", {"ref": ["id"]}]}}}, "Report": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Job Application Assessment Report", "@EntityRelationship.entityType": "sap.sf.recruiting:Report", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Assessment"}, "type": "cds.Integer64", "key": true}, "_Assessment": {"type": "cds.Association", "target": "Assessment", "cardinality": {"max": 1}, "on": [{"ref": ["_Assessment", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Assessment Report Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:ReportId"}, "statusId": {"type": "cds.Integer64", "@EndUserText.label": "Assessment Status", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "statusDate": {"type": "cds.DateTime", "@EndUserText.label": "Assessment Status Date"}, "statusDetails": {"type": "cds.String", "@EndUserText.label": "Status of assessment", "length": 1024}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Assessment Report Creation Date"}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Assessment Report Created By", "length": 45, "@PersonalData.isPotentiallyPersonal": true}, "details": {"type": "cds.Composition", "target": "Detail", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Assessment Report details", "on": [{"ref": ["details", "_id"]}, "=", {"ref": ["id"]}]}}}, "StatusDetail": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Status Details", "@EntityRelationship.entityType": "sap.sf.recruiting:StatusDetail", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_<PERSON><PERSON><PERSON><PERSON>"}, "type": "cds.Integer64", "key": true}, "_BackgroundCheck": {"type": "cds.Association", "target": "<PERSON><PERSON><PERSON><PERSON>", "cardinality": {"max": 1}, "on": [{"ref": ["_<PERSON><PERSON><PERSON><PERSON>", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Background Status Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:StatusDetailId"}, "name": {"type": "cds.String", "@EndUserText.label": "Background Check Name", "length": 256}, "status": {"type": "cds.String", "@EndUserText.label": "Background Check Status", "length": 256}, "type": {"type": "cds.String", "@EndUserText.label": "Background Check Type", "length": 1}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created Date"}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}}}, "Skill": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "<PERSON><PERSON> Details", "@EntityRelationship.entityType": "sap.sf.recruiting:<PERSON><PERSON>", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_SkillProfile"}, "type": "cds.Integer64", "key": true}, "_SkillProfile": {"type": "cds.Association", "target": "SkillProfile", "cardinality": {"max": 1}, "on": [{"ref": ["_SkillProfile", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Skill Id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:SkillId"}, "code": {"type": "cds.String", "@EndUserText.label": "Skill Code", "length": 128}, "score": {"type": "cds.Decimal", "@EndUserText.label": "Individual Skill Score", "precision": 34, "scale": 3}, "assignmentMode": {"type": "cds.Integer64", "@EndUserText.label": "Assignment Mode", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "priority": {"type": "cds.Integer64", "@EndUserText.label": "Priority", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "context": {"type": "cds.String", "@EndUserText.label": "Skill Context", "length": 1000}}}, "Detail": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Job Application Assessment Report Details", "@EntityRelationship.entityType": "sap.sf.recruiting:Detail", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Report"}, "type": "cds.Integer64", "key": true}, "_Report": {"type": "cds.Association", "target": "Report", "cardinality": {"max": 1}, "on": [{"ref": ["_Report", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Assessment report detail id", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:DetailId"}, "key": {"type": "cds.String", "@EndUserText.label": "Report Field Key", "length": 256}, "value": {"type": "cds.String", "@EndUserText.label": "Report Field Value", "length": 256}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Assessment Report detail Creation Date"}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Assessment report detail Created By", "length": 45, "@PersonalData.isPotentiallyPersonal": true}}}}}