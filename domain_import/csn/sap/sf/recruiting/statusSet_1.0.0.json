{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/recruiting/application/v1/metadata/statusSet", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "StatusSet", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.recruiting", "document": {"version": "1.0.0"}}, "definitions": {"StatusSet": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Status Set", "@EntityRelationship.entityType": "sap.sf.recruiting:StatusSet", "@EntityRelationship.entityIds": [{"name": "StatusSet", "propertyTypes": ["sap.sf.recruiting:StatusSetId"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Status Set ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:StatusSetId"}, "name": {"type": "cds.String", "@EndUserText.label": "Status Set Name", "length": 100}, "default": {"type": "cds.String", "@EndUserText.label": "Default Flag", "length": 1}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "modifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Last Modified by", "length": 100, "@PersonalData.isPotentiallyPersonal": true}}}}}