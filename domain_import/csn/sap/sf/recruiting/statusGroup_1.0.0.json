{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/recruiting/application/v1/metadata/statusGroup", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "StatusGroup", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.recruiting", "document": {"version": "1.0.0"}}, "definitions": {"StatusGroup": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Status Group", "@EntityRelationship.entityType": "sap.sf.recruiting:StatusGroup", "@EntityRelationship.entityIds": [{"name": "StatusGroup", "propertyTypes": ["sap.sf.recruiting:StatusGroupId"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Status Group ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:StatusGroupId"}, "statusSetId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.recruiting:statusSet", "referencedPropertyType": "sap.sf.recruiting:statusSetId"}], "type": "cds.Integer64", "@EndUserText.label": "Status Set ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "name": {"type": "cds.String", "@EndUserText.label": "Group Name", "length": 100}, "type": {"type": "cds.String", "@EndUserText.label": "Group Type", "length": 100}, "enabled": {"type": "cds.String", "@EndUserText.label": "Enabled", "length": 1}, "required": {"type": "cds.String", "@EndUserText.label": "Required", "length": 1}, "ordinal": {"type": "cds.Integer64", "@EndUserText.label": "Group Order", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "modifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Last Modified by", "length": 100, "@PersonalData.isPotentiallyPersonal": true}, "statusGroupLabels": {"type": "cds.Composition", "target": "StatusGroupLabel", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Status Group Localized Label", "on": [{"ref": ["statusGroupLabels", "_id"]}, "=", {"ref": ["id"]}]}}}, "StatusGroupLabel": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Status Group Localized Label", "@EntityRelationship.entityType": "sap.sf.recruiting:StatusGroupLabel", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_StatusGroup"}, "type": "cds.Integer64", "key": true}, "_StatusGroup": {"type": "cds.Association", "target": "StatusGroup", "cardinality": {"max": 1}, "on": [{"ref": ["_StatusGroup", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Status Group Label ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.recruiting:StatusGroupLabelId"}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "length": 32}, "labelText": {"type": "cds.String", "@EndUserText.label": "Group Label", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "modifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileLegacyId"}], "type": "cds.String", "@EndUserText.label": "Last Modified by", "length": 100, "@PersonalData.isPotentiallyPersonal": true}}}}}