{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/onboarding/v1/metadata/processSummary", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "ProcessSummary", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.onboarding", "document": {"version": "1.0.0"}}, "definitions": {"ProcessSummary": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Process User Task", "@EntityRelationship.entityType": "sap.sf.onboarding:ProcessSummary", "@EntityRelationship.entityIds": [{"name": "ProcessSummary", "propertyTypes": ["sap.sf.onboarding:ProcessSummaryId"]}], "elements": {"taskId": {"type": "cds.String", "@EndUserText.label": "Task ID", "length": 128}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Internal ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.onboarding:ProcessSummaryId"}, "refTaskId": {"type": "cds.String", "@EndUserText.label": "Reference Task ID", "length": 255}, "type": {"type": "cds.String", "@EndUserText.label": "Type", "length": 128}, "category": {"type": "cds.String", "@EndUserText.label": "Category", "length": 128}, "startDate": {"type": "cds.Date", "@EndUserText.label": "Start Date"}, "endDate": {"type": "cds.Date", "@EndUserText.label": "End Date"}, "completedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Completed By", "length": 100}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "taskDueDate": {"type": "cds.Date", "@EndUserText.label": "Task Due Date"}, "restartReason": {"type": "cds.String", "@EndUserText.label": "<PERSON>art Reason", "length": 128}, "externallyManaged": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Externally Managed"}, "subjectUser": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Subject User", "length": 100}, "responsible": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Responsible User", "length": 100}, "process": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.onboarding:process", "referencedPropertyType": "sap.sf.onboarding:processId"}], "type": "cds.Integer64", "@EndUserText.label": "Process"}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdDate": {"type": "cds.DateTime", "@EndUserText.label": "Created Date"}, "lastModifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "lastModifiedDate": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "taskTitle": {"type": "cds.String", "@EndUserText.label": "Task Title", "length": 255}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255}, "proxyUser": {"type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "actionUrl": {"type": "cds.String", "@EndUserText.label": "Action URL", "length": 512}, "complianceProcessId": {"type": "cds.String", "@EndUserText.label": "Compliance Process ID", "length": 255}, "taskDefinitionId": {"type": "cds.String", "@EndUserText.label": "Task Definition ID", "length": 255}, "taskFlowId": {"type": "cds.String", "@EndUserText.label": "Task Flow ID", "length": 255}, "participantType": {"type": "cds.String", "@EndUserText.label": "Participant Type", "length": 128}, "onboardingProcessId": {"type": "cds.String", "@EndUserText.label": "onboardingProcessId", "length": 255}, "comment": {"type": "cds.String", "@EndUserText.label": "comment", "length": 1000}, "postProcessingStatus": {"type": "cds.String", "@EndUserText.label": "postProcessingStatus", "length": 128}, "extensionTaskRefId": {"type": "cds.String", "@EndUserText.label": "extensionTaskRefId", "length": 255}}}}}