{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/onboarding/v1/metadata/formI9", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "FormI9", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.onboarding", "document": {"version": "1.0.0"}}, "definitions": {"FormI9": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "I-9 User Data", "@EntityRelationship.entityType": "sap.sf.onboarding:FormI9", "@EntityRelationship.entityIds": [{"name": "FormI9", "propertyTypes": ["sap.sf.onboarding:FormI9Id"]}], "elements": {"externalCode": {"type": "cds.String", "@EndUserText.label": "External Code", "length": 128}, "id": {"type": "cds.Integer64", "@EndUserText.label": "internalId", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.onboarding:FormI9Id"}, "process": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.onboarding:process", "referencedPropertyType": "sap.sf.onboarding:processId"}], "type": "cds.Integer64", "@EndUserText.label": "Process"}, "complianceProcess": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.onboarding:complianceProcess", "referencedPropertyType": "sap.sf.onboarding:complianceProcessId"}], "type": "cds.Integer64", "@EndUserText.label": "Compliance Process"}, "user": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "User", "length": 100}, "bpeProcessInstanceId": {"type": "cds.String", "@EndUserText.label": "Business Process Engine Process Instance ID", "length": 255}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "citizenshipType": {"type": "cds.String", "@EndUserText.label": "CitizenShip Type", "length": 128}, "alienRegistrationNumber": {"type": "cds.String", "@EndUserText.label": "Alien Registration Number", "length": 255}, "uscisNumber": {"type": "cds.String", "@EndUserText.label": "U.S. Citizenship and Immigration Services Number", "length": 255}, "i94AdministrationNum": {"type": "cds.String", "@EndUserText.label": "I-94 Administration Number", "length": 255}, "i94ExpirationDate": {"type": "cds.Date", "@EndUserText.label": "Date of Employment Authorization Expiration"}, "passportNumber": {"type": "cds.String", "@EndUserText.label": "Passport Number", "length": 255}, "countryOfIssue": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:country", "referencedPropertyType": "sap.sf.foundationobjects:countryId"}], "type": "cds.Integer64", "@EndUserText.label": "Country/Region of Issue"}, "employmentEligExpDate": {"type": "cds.Date", "@EndUserText.label": "Employment Eligibility Expiration Date"}, "visaType": {"type": "cds.String", "@EndUserText.label": "Visa Type", "length": 128}, "visaNum": {"type": "cds.String", "@EndUserText.label": "Visa Number", "length": 255}, "visaExpirationDate": {"type": "cds.Date", "@EndUserText.label": "Visa Expiration Date"}, "complianceForm": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:ComplianceForm", "referencedPropertyType": "sap.sf.extensibility:ComplianceFormId"}], "type": "cds.Integer64", "@EndUserText.label": "Compliance Form"}, "lastModifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "lastModifiedDate": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "protectedStatus": {"type": "cds.String", "@EndUserText.label": "Protected Status", "length": 128}, "ctzOfFreeAssocState": {"type": "cds.String", "@EndUserText.label": "Citizen of Free Associated State", "length": 128}, "personId": {"type": "cds.String", "@EndUserText.label": "Person ID", "length": 255}, "eVerifyData": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.onboarding:eVerify", "referencedPropertyType": "sap.sf.onboarding:eVerifyId"}], "type": "cds.Integer64", "@EndUserText.label": "E-Verify Data"}, "taskDueDate": {"type": "cds.Date", "@EndUserText.label": "Due Date"}, "i129PetitionFilled": {"type": "cds.String", "@EndUserText.label": "I-129 Petition Filled", "length": 255}, "h1BEmployee": {"type": "cds.String", "@EndUserText.label": "H1B Employee", "length": 255}, "i129SubmittedDate": {"type": "cds.Date", "@EndUserText.label": "I-129 Submission Date"}, "i129Document": {"type": "cds.String", "@EndUserText.label": "I-129 Document", "length": 255}, "additionalInfo1": {"type": "cds.String", "@EndUserText.label": "Additional Info 1", "length": 255}, "additionalInfo2": {"type": "cds.String", "@EndUserText.label": "Additional Info 2", "length": 255}, "additionalInfo3": {"type": "cds.String", "@EndUserText.label": "Additional Info 3", "length": 255}, "additionalInfo4": {"type": "cds.String", "@EndUserText.label": "Additional Info 4", "length": 255}, "additionalInfo5": {"type": "cds.String", "@EndUserText.label": "Additional Info 5", "length": 255}, "additionalInfo6": {"type": "cds.String", "@EndUserText.label": "Additional Info 6", "length": 255}, "additionalInfo7": {"type": "cds.String", "@EndUserText.label": "Additional Info 7", "length": 255}, "additionalInfo8": {"type": "cds.String", "@EndUserText.label": "Additional Info 8", "length": 255}, "additionalInfo9": {"type": "cds.String", "@EndUserText.label": "Additional Info 9", "length": 255}, "additionalInfo10": {"type": "cds.String", "@EndUserText.label": "Additional Info 10", "length": 255}, "firstName": {"type": "cds.String", "@EndUserText.label": "First Name", "length": 255}, "lastName": {"type": "cds.String", "@EndUserText.label": "Last Name", "length": 255}, "middleName": {"type": "cds.String", "@EndUserText.label": "Middle Name", "length": 255}, "otherName": {"type": "cds.String", "@EndUserText.label": "Other Name", "length": 255}, "address": {"type": "cds.String", "@EndUserText.label": "Address", "length": 255}, "apartmentNumber": {"type": "cds.String", "@EndUserText.label": "Apartment Number", "length": 255}, "city": {"type": "cds.String", "@EndUserText.label": "City", "length": 255}, "state": {"type": "cds.String", "@EndUserText.label": "State", "length": 255}, "zipCode": {"type": "cds.String", "@EndUserText.label": "Zip Code", "length": 255}, "dateOfBirth": {"type": "cds.Date", "@EndUserText.label": "Date Of Birth"}, "ssn": {"type": "cds.String", "@EndUserText.label": "Social Security Number", "length": 255}, "emailAddress": {"type": "cds.String", "@EndUserText.label": "Email Address", "length": 255}, "phoneNumber": {"type": "cds.String", "@EndUserText.label": "Phone Number", "length": 255}, "employerTitle": {"type": "cds.String", "@EndUserText.label": "Employer Title", "length": 255}, "employerBusiness": {"type": "cds.String", "@EndUserText.label": "Employer Business", "length": 255}, "employerFirstName": {"type": "cds.String", "@EndUserText.label": "Employer First Name", "length": 255}, "employerLastName": {"type": "cds.String", "@EndUserText.label": "Employer Last Name", "length": 255}, "employerAddress": {"type": "cds.String", "@EndUserText.label": "Employer Address", "length": 255}, "employerCity": {"type": "cds.String", "@EndUserText.label": "Employer City", "length": 255}, "employerState": {"type": "cds.String", "@EndUserText.label": "Employer State", "length": 255}, "employerZipCode": {"type": "cds.String", "@EndUserText.label": "Employer Zip Code", "length": 255}, "remote": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "remote"}, "reasonForRemote": {"type": "cds.String", "@EndUserText.label": "reasonForRemote", "length": 255}, "documentId": {"type": "cds.String", "@EndUserText.label": "documentId", "length": 255}, "correctI9Reason": {"type": "cds.String", "@EndUserText.label": "correctI9Reason", "length": 255}, "hireDate": {"type": "cds.Date", "@EndUserText.label": "hireDate"}, "initiationBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "initiationBy", "length": 100}, "initiationDate": {"type": "cds.Date", "@EndUserText.label": "initiationDate"}, "reasonForDelay": {"type": "cds.String", "@EndUserText.label": "reason<PERSON><PERSON><PERSON><PERSON><PERSON>", "length": 255}, "alienExpiryDateApplicable": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "alienExpiryDateApplicable"}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdDate": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "proxyUser": {"type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "altProcedure": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "altProcedure"}, "remoteDocumentSubmission": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "remoteDocumentSubmission"}, "listADocuments": {"type": "cds.Composition", "target": "I9Document", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Documents", "on": [{"ref": ["listADocuments", "_id"]}, "=", {"ref": ["id"]}]}, "listBDocuments": {"type": "cds.Composition", "target": "I9Document", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "List B Documents", "on": [{"ref": ["listBDocuments", "_id"]}, "=", {"ref": ["id"]}]}, "listCDocuments": {"type": "cds.Composition", "target": "I9Document", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "List C Documents", "on": [{"ref": ["listCDocuments", "_id"]}, "=", {"ref": ["id"]}]}, "translators": {"type": "cds.Composition", "target": "I9Translator", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Translators", "on": [{"ref": ["translators", "_id"]}, "=", {"ref": ["id"]}]}, "listADocumentScans": {"type": "cds.Composition", "target": "I9DocumentScan", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "listADocumentScans", "on": [{"ref": ["listADocumentScans", "_id"]}, "=", {"ref": ["id"]}]}, "listBDocumentScans": {"type": "cds.Composition", "target": "I9DocumentScan", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "listBDocumentScans", "on": [{"ref": ["listBDocumentScans", "_id"]}, "=", {"ref": ["id"]}]}, "listCDocumentScans": {"type": "cds.Composition", "target": "I9DocumentScan", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "listCDocumentScans", "on": [{"ref": ["listCDocumentScans", "_id"]}, "=", {"ref": ["id"]}]}}}, "I9Document": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "I-9 Document", "@EntityRelationship.entityType": "sap.sf.onboarding:I9Document", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_FormI9"}, "type": "cds.Integer64", "key": true}, "_FormI9": {"type": "cds.Association", "target": "FormI9", "cardinality": {"max": 1}, "on": [{"ref": ["_FormI9", "id"]}, "=", {"ref": ["_id"]}]}, "externalCode": {"type": "cds.String", "@EndUserText.label": "External Code", "length": 128}, "id": {"type": "cds.Integer64", "@EndUserText.label": "internalId", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "documentType": {"type": "cds.String", "@EndUserText.label": "Document Type", "length": 128}, "documentNumber": {"type": "cds.String", "@EndUserText.label": "Document Number", "length": 255}, "documentIssuer": {"type": "cds.String", "@EndUserText.label": "Document Issuer", "length": 255}, "expirationDate": {"type": "cds.Date", "@EndUserText.label": "Expiration Date"}, "expirationType": {"type": "cds.String", "@EndUserText.label": "expirationType", "length": 128}, "attachment": {"type": "cds.String", "@EndUserText.label": "Attachment", "length": 255}, "user": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "User", "length": 100}, "dhsDocument": {"type": "cds.String", "@EndUserText.label": "U.S. Department of Homeland Security Document", "length": 255}, "createdDate": {"type": "cds.DateTime", "@EndUserText.label": "Creation Date"}, "entityId": {"type": "cds.String", "@EndUserText.label": "Entity ID", "length": 255}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.onboarding:I9DocumentRecordId"}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "objectType": {"type": "cds.String", "@EndUserText.label": "Object Type", "length": 255}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "lastModifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "lastModifiedDate": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "proxyUser": {"type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}}}, "I9Translator": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "I-9 Translator", "@EntityRelationship.entityType": "sap.sf.onboarding:I9Translator", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_FormI9"}, "type": "cds.Integer64", "key": true}, "_FormI9": {"type": "cds.Association", "target": "FormI9", "cardinality": {"max": 1}, "on": [{"ref": ["_FormI9", "id"]}, "=", {"ref": ["_id"]}]}, "externalCode": {"type": "cds.String", "@EndUserText.label": "External Code", "length": 128}, "id": {"type": "cds.Integer64", "@EndUserText.label": "internalId", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "firstName": {"type": "cds.String", "@EndUserText.label": "First Name", "length": 255}, "lastName": {"type": "cds.String", "@EndUserText.label": "Last Name", "length": 255}, "address1": {"type": "cds.String", "@EndUserText.label": "Address 1", "length": 255}, "address2": {"type": "cds.String", "@EndUserText.label": "Address 2", "length": 255}, "city": {"type": "cds.String", "@EndUserText.label": "City", "length": 255}, "state": {"type": "cds.String", "@EndUserText.label": "State", "length": 255}, "zipCode": {"type": "cds.String", "@EndUserText.label": "Zip Code", "length": 255}, "emailAddress": {"type": "cds.String", "@EndUserText.label": "Email Address", "length": 255}, "entityId": {"type": "cds.String", "@EndUserText.label": "Entity ID", "length": 255}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.onboarding:I9TranslatorRecordId"}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "objectType": {"type": "cds.String", "@EndUserText.label": "Object Type", "length": 255}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdDate": {"type": "cds.DateTime", "@EndUserText.label": "Created On"}, "lastModifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "lastModifiedDate": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "proxyUser": {"type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}}}, "I9DocumentScan": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "I9DocumentScan", "@EntityRelationship.entityType": "sap.sf.onboarding:I9DocumentScan", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_FormI9"}, "type": "cds.Integer64", "key": true}, "_FormI9": {"type": "cds.Association", "target": "FormI9", "cardinality": {"max": 1}, "on": [{"ref": ["_FormI9", "id"]}, "=", {"ref": ["_id"]}]}, "externalCode": {"type": "cds.String", "@EndUserText.label": "externalCode", "length": 128}, "id": {"type": "cds.Integer64", "@EndUserText.label": "internalId", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "documentType": {"type": "cds.String", "@EndUserText.label": "documentType", "length": 128}, "attachment": {"type": "cds.String", "@EndUserText.label": "attachment", "length": 255}, "user": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "user", "length": 100}, "createdDate": {"type": "cds.DateTime", "@EndUserText.label": "createdDate"}, "entityId": {"type": "cds.String", "@EndUserText.label": "Entity ID", "length": 255}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.onboarding:I9DocumentScanRecordId"}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "objectType": {"type": "cds.String", "@EndUserText.label": "Object Type", "length": 255}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "lastModifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "lastModifiedDate": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "proxyUser": {"type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}}}}}