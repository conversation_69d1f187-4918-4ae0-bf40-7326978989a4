{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/onboarding/v1/metadata/processTrigger", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "ProcessTrigger", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.onboarding", "document": {"version": "1.0.0"}}, "definitions": {"ProcessTrigger": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Process Trigger", "@EntityRelationship.entityType": "sap.sf.onboarding:ProcessTrigger", "@EntityRelationship.entityIds": [{"name": "ProcessTrigger", "propertyTypes": ["sap.sf.onboarding:ProcessTriggerId"]}], "elements": {"triggerId": {"type": "cds.String", "@EndUserText.label": "Process Initiation ID", "length": 128}, "id": {"type": "cds.Integer64", "@EndUserText.label": "internalId", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.onboarding:ProcessTriggerId"}, "triggerType": {"type": "cds.String", "@EndUserText.label": "Process Initiation Type", "length": 128}, "triggerStatus": {"type": "cds.String", "@EndUserText.label": "Process Initiation Status", "length": 128}, "bpeProcessInstanceId": {"type": "cds.String", "@EndUserText.label": "BPE Process Instance ID", "length": 128}, "rcmJobReqId": {"type": "cds.Integer64", "@EndUserText.label": "Job Requisition ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "rcmApplicationId": {"type": "cds.Integer64", "@EndUserText.label": "Job Application ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "rcmOfferId": {"type": "cds.Integer64", "@EndUserText.label": "Job Offer ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "rcmCandidateId": {"type": "cds.Integer64", "@EndUserText.label": "Candidate ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "rcmPrimaryEmail": {"type": "cds.String", "@EndUserText.label": "Primary Email", "length": 255}, "rcmHiringMgr": {"type": "cds.String", "@EndUserText.label": "Hiring Manager ID", "length": 128}, "rcmStartDate": {"type": "cds.String", "@EndUserText.label": "Employee Start Date", "length": 32}, "rcmCompany": {"type": "cds.String", "@EndUserText.label": "Company", "length": 128}, "atsApplicationId": {"type": "cds.String", "@EndUserText.label": "Application Tracking System Job Application ID", "length": 128}, "atsUserId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Application Tracking System User ID", "length": 100}, "rehireUser": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Rehire User", "length": 100}, "hireType": {"type": "cds.String", "@EndUserText.label": "Hire Type", "length": 128}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "createdDate": {"type": "cds.DateTime", "@EndUserText.label": "createdDate"}, "lastModifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "lastModifiedDate": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "proxyUser": {"type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "onboardingInfoId": {"type": "cds.String", "@EndUserText.label": "onboardingInfoId", "length": 255}, "jobInfoId": {"type": "cds.Integer64", "@EndUserText.label": "jobInfoId", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "workflowId": {"type": "cds.Integer64", "@EndUserText.label": "workflowId", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}}}}}