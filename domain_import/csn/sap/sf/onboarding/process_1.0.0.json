{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/onboarding/v1/metadata/process", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "Process", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.onboarding", "document": {"version": "1.0.0"}}, "definitions": {"Process": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Process", "@EntityRelationship.entityType": "sap.sf.onboarding:Process", "@EntityRelationship.entityIds": [{"name": "Process", "propertyTypes": ["sap.sf.onboarding:ProcessId"]}], "elements": {"processId": {"type": "cds.String", "@EndUserText.label": "Process ID", "length": 128}, "id": {"type": "cds.Integer64", "@EndUserText.label": "internalId", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.onboarding:ProcessId"}, "createdDate": {"type": "cds.DateTime", "@EndUserText.label": "createdDate"}, "user": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "User", "length": 100}, "employeePersonId": {"type": "cds.Integer64", "@EndUserText.label": "Person ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "length": 128}, "targetDate": {"type": "cds.Date", "@EndUserText.label": "Target Date"}, "processType": {"type": "cds.String", "@EndUserText.label": "Process Type", "length": 128}, "processStatus": {"type": "cds.String", "@EndUserText.label": "Process Status", "length": 128}, "activitiesStatus": {"type": "cds.String", "@EndUserText.label": "Tasks Status", "length": 128}, "manager": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Manager", "length": 100}, "managerPersonId": {"type": "cds.Integer64", "@EndUserText.label": "Hiring Manager Person ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "activitiesConfig": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:ONB2ActivitiesConfig", "referencedPropertyType": "sap.sf.extensibility:ONB2ActivitiesConfigId"}], "type": "cds.Integer64", "@EndUserText.label": "Onboarding Tasks Configuration"}, "offboardingActivitiesConfig": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:ONB2OffboardingActivitiesConfig", "referencedPropertyType": "sap.sf.extensibility:ONB2OffboardingActivitiesConfigId"}], "type": "cds.Integer64", "@EndUserText.label": "Offboarding Tasks Configuration"}, "onboardingHireStatus": {"type": "cds.String", "@EndUserText.label": "Hire Status", "length": 128}, "onboardingHiredDate": {"type": "cds.DateTime", "@EndUserText.label": "Hire Date"}, "onboardingInternalHire": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Internal Hire"}, "bpeProcessInstanceId": {"type": "cds.String", "@EndUserText.label": "Business Process Engine Process Instance ID", "length": 128}, "customDataCollectionConfig": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:ONB2DataCollectionConfig", "referencedPropertyType": "sap.sf.extensibility:ONB2DataCollectionConfigId"}], "type": "cds.Integer64", "@EndUserText.label": "customDataCollectionConfig"}, "cancelOnboardingReason": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueId"}, {"referencedEntityType": "sap.sf.extensibility:ONB2CancelOnboardingReasonPickList", "referencedPropertyType": "sap.sf.extensibility:ONB2CancelOnboardingReasonPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Reason for Onboarding Cancellation"}, "cancellationComment": {"type": "cds.String", "@EndUserText.label": "Process Cancellation Comments", "length": 255}, "processTrigger": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.onboarding:processTrigger", "referencedPropertyType": "sap.sf.onboarding:processTriggerId"}], "type": "cds.Integer64", "@EndUserText.label": "Process Trigger"}, "processRestarted": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Process Restarted"}, "startDate": {"type": "cds.DateTime", "@EndUserText.label": "Process Start Date"}, "endDate": {"type": "cds.DateTime", "@EndUserText.label": "Process End Date"}, "cancellationDate": {"type": "cds.Date", "@EndUserText.label": "Cancellation Date"}, "cancelOffboardingReason": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:pickListValue", "referencedPropertyType": "sap.sf.extensibility:pickListValueId"}, {"referencedEntityType": "sap.sf.extensibility:ONB2CancelOffboardingReasonPickList", "referencedPropertyType": "sap.sf.extensibility:ONB2CancelOffboardingReasonPickListListId"}], "type": "cds.Integer64", "@EndUserText.label": "Reason for Offboarding Cancellation"}, "processVariant": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:ONB2ProcessVariant", "referencedPropertyType": "sap.sf.extensibility:ONB2ProcessVariantId"}], "type": "cds.Integer64", "@EndUserText.label": "Process Variant"}, "cancelledDueToRestart": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Cancelled Due to <PERSON><PERSON>"}, "cancelEventReason": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.foundationobjects:eventReason", "referencedPropertyType": "sap.sf.foundationobjects:eventReasonId"}], "type": "cds.Integer64", "@EndUserText.label": "Event Reason for Onboarding Cancellation"}, "targetSystem": {"type": "cds.String", "@EndUserText.label": "targetSystem", "length": 128}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "lastModifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "lastModifiedDate": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "proxyUser": {"type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "stableId": {"type": "cds.String", "@EndUserText.label": "Onboarding Stable ID", "length": 128}, "onboardingAutoHireStatus": {"type": "cds.String", "@EndUserText.label": "Hiring Type", "length": 128}, "processRestartNeeded": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "processRestartNeeded"}, "cancellationSource": {"type": "cds.String", "@EndUserText.label": "cancellationSource", "length": 128}, "alumniStatus": {"type": "cds.String", "@EndUserText.label": "alumniStatus", "length": 128}, "processTasks": {"type": "cds.Composition", "target": "ONB2ProcessTask", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Process Tasks", "on": [{"ref": ["processTasks", "_id"]}, "=", {"ref": ["id"]}]}}}, "ONB2ProcessTask": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Process Task", "@EntityRelationship.entityType": "sap.sf.onboarding:ONB2ProcessTask", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Process"}, "type": "cds.Integer64", "key": true}, "_Process": {"type": "cds.Association", "target": "Process", "cardinality": {"max": 1}, "on": [{"ref": ["_Process", "id"]}, "=", {"ref": ["_id"]}]}, "taskId": {"type": "cds.String", "@EndUserText.label": "Process Task ID", "length": 128}, "id": {"type": "cds.Integer64", "@EndUserText.label": "internalId", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "type": {"type": "cds.String", "@EndUserText.label": "Process Task Type", "length": 128}, "startDate": {"type": "cds.DateTime", "@EndUserText.label": "Process Task Start Date"}, "endDate": {"type": "cds.DateTime", "@EndUserText.label": "Process Task End Date"}, "responsibilityConfig": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.extensibility:ONB2ResponsibilityConfig", "referencedPropertyType": "sap.sf.extensibility:ONB2ResponsibilityConfigId"}], "type": "cds.Integer64", "@EndUserText.label": "responsibilityConfig"}, "completedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Completed By", "length": 100}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "entityId": {"type": "cds.String", "@EndUserText.label": "Entity ID", "length": 255}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.onboarding:ONB2ProcessTaskRecordId"}, "objectType": {"type": "cds.String", "@EndUserText.label": "Object Type", "length": 255}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "mdfSystemCreatedBy", "length": 100}, "createdDate": {"type": "cds.DateTime", "@EndUserText.label": "mdfSystemCreatedDate"}, "lastModifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "mdfSystemLastModifiedBy", "length": 100}, "lastModifiedDate": {"type": "cds.DateTime", "@EndUserText.label": "mdfSystemLastModifiedDate"}, "proxyUser": {"type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "taskDueDate": {"type": "cds.Date", "@EndUserText.label": "taskDueDate"}, "comment": {"type": "cds.String", "@EndUserText.label": "comment", "length": 1000}, "extensionTaskRefId": {"type": "cds.String", "@EndUserText.label": "extensionTaskRefId", "length": 255}}}}}