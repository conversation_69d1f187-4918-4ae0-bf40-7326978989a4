{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/onboarding/v1/metadata/complianceProcess", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "ComplianceProcess", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.onboarding", "document": {"version": "1.0.0"}}, "definitions": {"ComplianceProcess": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Compliance Process", "@EntityRelationship.entityType": "sap.sf.onboarding:ComplianceProcess", "@EntityRelationship.entityIds": [{"name": "ComplianceProcess", "propertyTypes": ["sap.sf.onboarding:ComplianceProcessId"]}], "elements": {"processId": {"type": "cds.String", "@EndUserText.label": "Process ID", "length": 128}, "id": {"type": "cds.Integer64", "@EndUserText.label": "internalId", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.onboarding:ComplianceProcessId"}, "createdDate": {"type": "cds.DateTime", "@EndUserText.label": "Created Date"}, "user": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "User", "length": 100}, "processInitiatorType": {"type": "cds.String", "@EndUserText.label": "Process Initiation Type", "length": 128}, "processInitiatorId": {"type": "cds.String", "@EndUserText.label": "Process Initiation Type ID", "length": 128}, "processStatus": {"type": "cds.String", "@EndUserText.label": "Compliance Process Status", "length": 128}, "onboardingProcess": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.onboarding:process", "referencedPropertyType": "sap.sf.onboarding:processId"}], "type": "cds.Integer64", "@EndUserText.label": "Onboarding Process"}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "lastModifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "lastModifiedDate": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "proxyUser": {"type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "processType": {"type": "cds.String", "@EndUserText.label": "Compliance Process Type", "length": 128}, "i9UserData": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.onboarding:formI9", "referencedPropertyType": "sap.sf.onboarding:formI9Id"}], "type": "cds.Integer64", "@EndUserText.label": "I-9 User Data"}, "eVerifyData": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.onboarding:eVerify", "referencedPropertyType": "sap.sf.onboarding:eVerifyId"}], "type": "cds.Integer64", "@EndUserText.label": "E-Verify Data"}, "complianceMasterId": {"type": "cds.String", "@EndUserText.label": "Compliance Stable ID", "length": 128}, "startDate": {"type": "cds.Date", "@EndUserText.label": "Start Date"}, "correctDataTriggered": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "correctDataTriggered"}}}}}