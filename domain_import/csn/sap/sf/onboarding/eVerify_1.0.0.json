{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/onboarding/v1/metadata/eVerify", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "EVerify", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.onboarding", "document": {"version": "1.0.0"}}, "definitions": {"EVerify": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "E-Verify Data", "@EntityRelationship.entityType": "sap.sf.onboarding:EVerify", "@EntityRelationship.entityIds": [{"name": "EVerify", "propertyTypes": ["sap.sf.onboarding:EVerifyId"]}], "elements": {"externalId": {"type": "cds.String", "@EndUserText.label": "External ID", "length": 128}, "id": {"type": "cds.Integer64", "@EndUserText.label": "internalId", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}, "key": true, "@EntityRelationship.propertyType": "sap.sf.onboarding:EVerifyId"}, "subjectUser": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Subject User", "length": 100}, "i9userdata": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.onboarding:formI9", "referencedPropertyType": "sap.sf.onboarding:formI9Id"}], "type": "cds.Integer64", "@EndUserText.label": "I-9 User Data"}, "caseNumber": {"type": "cds.String", "@EndUserText.label": "Case Number", "length": 255}, "clientSoftwareVersion": {"type": "cds.String", "@EndUserText.label": "Client Software Version", "length": 255}, "duplicateContinueReason": {"type": "cds.String", "@EndUserText.label": "Reason for Duplicate Case", "length": 255}, "firstName": {"type": "cds.String", "@EndUserText.label": "First Name", "length": 255}, "middleName": {"type": "cds.String", "@EndUserText.label": "Middle Name", "length": 255}, "lastName": {"type": "cds.String", "@EndUserText.label": "Last Name", "length": 255}, "otherLastNamesUsed": {"type": "cds.String", "@EndUserText.label": "Other Last Name Used", "length": 255}, "dateOfBirth": {"type": "cds.Date", "@EndUserText.label": "Date of Birth"}, "employeeEmailAddress": {"type": "cds.String", "@EndUserText.label": "Employee Email Address", "length": 255}, "phoneNumber": {"type": "cds.String", "@EndUserText.label": "Phone Number", "length": 255}, "ssn": {"type": "cds.String", "@EndUserText.label": "Social Security Number", "length": 255}, "alienNumber": {"type": "cds.String", "@EndUserText.label": "Alien Number", "length": 255}, "clientCompanyId": {"type": "cds.Integer64", "@EndUserText.label": "Client Company ID", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "citizenShipStatusCode": {"type": "cds.String", "@EndUserText.label": "Citizenship Status Code", "length": 128}, "documentATypeCode": {"type": "cds.String", "@EndUserText.label": "Document A Type Code", "length": 128}, "documentBTypeCode": {"type": "cds.String", "@EndUserText.label": "Document B Type Code", "length": 128}, "documentCTypeCode": {"type": "cds.String", "@EndUserText.label": "Document C Type Code", "length": 128}, "documentSubTypeCode": {"type": "cds.String", "@EndUserText.label": "Document Sub Type Code", "length": 128}, "i94Number": {"type": "cds.String", "@EndUserText.label": "I-94 Number", "length": 255}, "i551Number": {"type": "cds.String", "@EndUserText.label": "I-551 Number", "length": 255}, "i766Number": {"type": "cds.String", "@EndUserText.label": "I-766 Number", "length": 255}, "usPassportNumber": {"type": "cds.String", "@EndUserText.label": "U.S. Passport Number", "length": 255}, "foreignPassportNumber": {"type": "cds.String", "@EndUserText.label": "Foreign Passport Number", "length": 255}, "documentBCNumber": {"type": "cds.String", "@EndUserText.label": "Document BC Number", "length": 255}, "expirationDate": {"type": "cds.Date", "@EndUserText.label": "Expiration Date"}, "countryCode": {"type": "cds.String", "@EndUserText.label": "Country/Region Code", "length": 255}, "usStateCode": {"type": "cds.String", "@EndUserText.label": "U.S. State Code", "length": 255}, "noExpirationDate": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "No Expiration Date"}, "visaNumber": {"type": "cds.String", "@EndUserText.label": "Visa Number", "length": 255}, "employerCaseId": {"type": "cds.String", "@EndUserText.label": "Employer Case ID", "length": 255}, "dateOfHire": {"type": "cds.Date", "@EndUserText.label": "Date of Hire"}, "reasonForDelayCode": {"type": "cds.String", "@EndUserText.label": "Reason For Delay Code", "length": 255}, "reasonForDelayDescription": {"type": "cds.String", "@EndUserText.label": "Reason for Delay Description", "length": 255}, "sevisNumber": {"type": "cds.String", "@EndUserText.label": "SEVIS Number", "length": 255}, "fanAttachmentId": {"type": "cds.String", "@EndUserText.label": "FAN Attachment ID", "length": 255}, "summaryFormAttachmentId": {"type": "cds.String", "@EndUserText.label": "Summary Form Attachment ID", "length": 255}, "complianceProcess": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.onboarding:complianceProcess", "referencedPropertyType": "sap.sf.onboarding:complianceProcessId"}], "type": "cds.Integer64", "@EndUserText.label": "Compliance Process"}, "companyName": {"type": "cds.String", "@EndUserText.label": "Company Name", "length": 255}, "city": {"type": "cds.String", "@EndUserText.label": "City", "length": 255}, "state": {"type": "cds.String", "@EndUserText.label": "State", "length": 255}, "initialResolution": {"type": "cds.String", "@EndUserText.label": "Initial Resolution", "length": 255}, "additionalResolution": {"type": "cds.String", "@EndUserText.label": "Additional Resolution", "length": 255}, "dhs3rdStepResolution": {"type": "cds.String", "@EndUserText.label": "Department of Homeland Security Step 3 Resolution", "length": 255}, "finalStatus": {"type": "cds.String", "@EndUserText.label": "Final Status", "length": 255}, "caseClosureDate": {"type": "cds.Date", "@EndUserText.label": "Case Closure Date"}, "caseClosureDescription": {"type": "cds.String", "@EndUserText.label": "Case Closure Description", "length": 255}, "photo": {"type": "cds.String", "@EndUserText.label": "Photo", "length": 255}, "englishFANDocument": {"type": "cds.String", "@EndUserText.label": "Further Action Notice Document (English)", "length": 255}, "spanishFANDocument": {"type": "cds.String", "@EndUserText.label": "Further Action Notice Document (Spanish)", "length": 255}, "englishRDCDocument": {"type": "cds.String", "@EndUserText.label": "Referral Date Confirmation Document (English)", "length": 255}, "spanishRDCDocument": {"type": "cds.String", "@EndUserText.label": "Referral Date Confirmation Document (Spanish)", "length": 255}, "i9ListADocumentAttachmentId": {"type": "cds.String", "@EndUserText.label": "Form I-9 List A Document Attachment ID", "length": 255}, "caseEligibilityStatement": {"type": "cds.String", "@EndUserText.label": "caseEligibilityStatement", "length": 255}, "caseStatusDisplay": {"type": "cds.String", "@EndUserText.label": "caseStatusDisplay", "length": 255}, "dhsReferralStatus": {"type": "cds.String", "@EndUserText.label": "dhsReferralStatus", "length": 255}, "ssaReferralStatus": {"type": "cds.String", "@EndUserText.label": "ssaReferralStatus", "length": 255}, "createdDate": {"type": "cds.DateTime", "@EndUserText.label": "createdDate"}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "created<PERSON>y", "length": 100}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "lastModifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "lastModifiedDate": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "proxyUser": {"type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "duplicateContinueReasonCode": {"type": "cds.String", "@EndUserText.label": "duplicateContinueReasonCode", "length": 255}, "tncInfoCorrectStatus": {"type": "cds.String", "@EndUserText.label": "tncInfoCorrectStatus", "length": 128}, "tncActionStatus": {"type": "cds.String", "@EndUserText.label": "tncActionStatus", "length": 128}, "tncStatusDate": {"type": "cds.Date", "@EndUserText.label": "tncStatusDate"}, "caseStatusDetails": {"type": "cds.Composition", "target": "EVerifyCaseStatusDetails", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "caseStatusDetails", "on": [{"ref": ["caseStatusDetails", "_id"]}, "=", {"ref": ["id"]}]}}}, "EVerifyCaseStatusDetails": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "E-Verify Case Status Details", "@EntityRelationship.entityType": "sap.sf.onboarding:EVerifyCaseStatusDetails", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_EVerify"}, "type": "cds.Integer64", "key": true}, "_EVerify": {"type": "cds.Association", "target": "EVerify", "cardinality": {"max": 1}, "on": [{"ref": ["_EVerify", "id"]}, "=", {"ref": ["_id"]}]}, "externalId": {"type": "cds.String", "@EndUserText.label": "External Code", "length": 128}, "id": {"type": "cds.Integer64", "@EndUserText.label": "internalId", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "subjectUser": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Subject User", "length": 100}, "caseNumber": {"type": "cds.String", "@EndUserText.label": "E-Verify Case Number", "length": 255}, "sequnceNumber": {"type": "cds.Integer64", "@EndUserText.label": "Sequence Number", "@Semantics.valueRange": {"minimum": "-9223372036854775808", "maximum": "9223372036854775807"}}, "createdBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "created<PERSON>y", "length": 100}, "createDate": {"type": "cds.DateTime", "@EndUserText.label": "createDate"}, "requestCaseStatus": {"type": "cds.String", "@EndUserText.label": "Request Case Status", "length": 128}, "responseCaseStatus": {"type": "cds.String", "@EndUserText.label": "Response to Case Status", "length": 128}, "caseEligibilityStatement": {"type": "cds.String", "@EndUserText.label": "Case Eligibility Statement", "length": 255}, "caseStatusDisplay": {"type": "cds.String", "@EndUserText.label": "Case Status Display", "length": 255}, "dhsReferralStatus": {"type": "cds.String", "@EndUserText.label": "U.S. Department of Homeland Security Referral Status", "length": 255}, "ssaReferralStatus": {"type": "cds.String", "@EndUserText.label": "Social Security Administration Referral Status", "length": 255}, "entityId": {"type": "cds.String", "@EndUserText.label": "Entity ID", "length": 255}, "recordId": {"type": "cds.String", "@EndUserText.label": "Record ID", "length": 255, "key": true, "@EntityRelationship.propertyType": "sap.sf.onboarding:EVerifyCaseStatusDetailsRecordId"}, "status": {"type": "cds.String", "@EndUserText.label": "Status", "length": 255}, "objectType": {"type": "cds.String", "@EndUserText.label": "Object Type", "length": 255}, "lastModifiedBy": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.workforce:workProfile", "referencedPropertyType": "sap.sf.workforce:workProfileId"}], "type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "lastModifiedDate": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Date"}, "proxyUser": {"type": "cds.String", "@EndUserText.label": "Proxy User", "length": 255}, "createdBySystem": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "createdBySystem"}}}}}