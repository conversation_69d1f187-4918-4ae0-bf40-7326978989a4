{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/performance/review/v1/metadata/feedbackExt", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "FeedbackExt", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.performance", "document": {"version": "1.0.0"}}, "definitions": {"feedbackExt": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Feedback Extension", "@EntityRelationship.entityType": "sap.sf.performance:FeedbackExt", "@EntityRelationship.entityIds": [{"name": "FeedbackExt", "propertyTypes": ["sap.sf.performance:FeedbackExtUUID"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Feedback Extension ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:FeedbackExtUUID"}, "formId": {"type": "cds.Integer64", "@EndUserText.label": "Form ID"}, "formContentId": {"type": "cds.Integer64", "@EndUserText.label": "Form Content ID"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 1024}, "itemType": {"type": "cds.Integer64", "@EndUserText.label": "Item Type"}, "itemId": {"type": "cds.Integer64", "@EndUserText.label": "Item ID"}, "type": {"type": "cds.Integer64", "@EndUserText.label": "Type"}, "key": {"type": "cds.String", "@EndUserText.label": "Key", "length": 256}, "valueType": {"type": "cds.Integer64", "@EndUserText.label": "Value Type"}, "numberValue": {"type": "cds.Decimal", "@EndUserText.label": "Number Value", "precision": 34, "scale": 1}, "stringValue": {"type": "cds.String", "@EndUserText.label": "String Value", "length": 5000}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Time"}}}}}