{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/goals/v1/metadata/audit", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "ObjectiveAudit", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.performance", "document": {"version": "1.0.0"}}, "definitions": {"ObjectiveAudit": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Objective Audit", "@ODM.entityName": "PerformanceObjectiveAudit", "@ODM.oid": "id", "@EntityRelationship.entityType": "sap.sf.performance:O<PERSON>ive<PERSON><PERSON><PERSON>", "@EntityRelationship.entityIds": [{"name": "ObjectiveAudit", "propertyTypes": ["sap.sf.performance:AuditId"]}], "elements": {"objectiveId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.performance:Objective", "referencedPropertyType": "sap.sf.performance:ObjectiveId"}], "type": "cds.Integer64", "@EndUserText.label": "Objective Id"}, "action": {"type": "cds.Integer64", "@EndUserText.label": "Action"}, "comment": {"type": "cds.String", "@EndUserText.label": "Comment"}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Audit ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:AuditId"}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified"}, "recipient": {"type": "cds.String", "@EndUserText.label": "Recipient"}, "sender": {"type": "cds.String", "@EndUserText.label": "Sender"}, "sendProxy": {"type": "cds.String", "@EndUserText.label": "Send Proxy"}, "createdBy": {"type": "cds.String", "@EndUserText.label": "Created By"}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created At"}}}}}