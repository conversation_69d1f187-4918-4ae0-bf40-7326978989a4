{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/performance/review/v1/metadata/form", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "Form", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.performance", "document": {"version": "1.0.0"}}, "definitions": {"Form": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "form resource", "@EntityRelationship.entityType": "sap.sf.performance:Form", "@EntityRelationship.entityIds": [{"name": "Form", "propertyTypes": ["sap.sf.performance:FormId"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Form ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:FormId"}, "templateId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.performance:FormTemplate", "referencedPropertyType": "sap.sf.performance:FormTemplateId"}], "type": "cds.Integer64", "@EndUserText.label": "Template ID"}, "originator": {"type": "cds.String", "@EndUserText.label": "Originator", "length": 100}, "status": {"type": "cds.Integer64", "@EndUserText.label": "Status"}, "title": {"type": "cds.String", "@EndUserText.label": "Title", "length": 512}, "type": {"type": "cds.String", "@EndUserText.label": "Type", "length": 32}, "subject": {"type": "cds.String", "@EndUserText.label": "Subject"}, "reviewStart": {"type": "cds.DateTime", "@EndUserText.label": "Review Start"}, "reviewEnd": {"type": "cds.DateTime", "@EndUserText.label": "Review End"}, "dueDate": {"type": "cds.DateTime", "@EndUserText.label": "Due Date"}, "lastAuditTrailId": {"type": "cds.Integer64", "@EndUserText.label": "Last Audit Trail ID"}, "rating": {"type": "cds.Decimal", "@EndUserText.label": "Rating", "precision": 34, "scale": 1}, "owner": {"type": "cds.String", "@EndUserText.label": "Owner", "length": 100}, "scaleId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.performance:RatingScale", "referencedPropertyType": "sap.sf.performance:RatingScaleId"}], "type": "cds.Integer64", "@EndUserText.label": "Scale ID"}, "rated": {"type": "cds.String", "@EndUserText.label": "Rated", "length": 2}, "version": {"type": "cds.String", "@EndUserText.label": "Version", "length": 12}, "achievedDate": {"type": "cds.DateTime", "@EndUserText.label": "Achieved Date"}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Time"}}}}}