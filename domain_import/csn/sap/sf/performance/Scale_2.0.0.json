{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/rating/v2/metadata/scale", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "Scale", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.performance", "document": {"version": "1.0.0"}}, "definitions": {"scale": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "scale resource", "@EntityRelationship.entityType": "sap.sf.performance:Scale", "@EntityRelationship.entityIds": [{"name": "Scale", "propertyTypes": ["sap.sf.performance:ScaleUUID"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Scale ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:ScaleUUID"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 32}, "source": {"type": "cds.Integer64", "@EndUserText.label": "Source"}, "type": {"type": "cds.String", "@EndUserText.label": "Type", "length": 32}, "status": {"type": "cds.Integer64", "@EndUserText.label": "Status"}, "defaultLocale": {"type": "cds.String", "@EndUserText.label": "Default Locale", "length": 32}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 4000}, "modifiedBy": {"type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created Date Time"}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Time"}, "values": {"type": "cds.Composition", "target": "scale_values", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Values", "on": [{"ref": ["values", "scale_id_virtual"]}, "=", {"ref": ["id"]}]}}}, "scale_values": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "scale value resource", "@EntityRelationship.entityType": "sap.sf.performance:scale_values", "elements": {"scale_id_virtual": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_scale"}, "type": "cds.Integer64", "key": true}, "_scale": {"type": "cds.Association", "target": "scale", "cardinality": {"max": 1}, "on": [{"ref": ["_scale", "id"]}, "=", {"ref": ["scale_id_virtual"]}]}, "scaleId": {"type": "cds.Integer64", "@EndUserText.label": "Scale ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:scale_valuesScaleId"}, "value": {"type": "cds.Decimal", "@EndUserText.label": "Value", "precision": 34, "scale": 1, "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:scale_valuesValue"}, "maxValue": {"type": "cds.Decimal", "@EndUserText.label": "Max Value", "precision": 34, "scale": 1}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "length": 32}, "shortName": {"type": "cds.String", "@EndUserText.label": "Short Name", "length": 128}, "longName": {"type": "cds.String", "@EndUserText.label": "Long Name", "length": 4000}, "imageId": {"type": "cds.Integer64", "@EndUserText.label": "Image ID"}, "texts": {"type": "cds.Composition", "target": "scaleValue_texts", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Texts", "on": [{"ref": ["texts", "scale_values_scaleId_virtual"]}, "=", {"ref": ["scaleId"]}]}}}, "scaleValue_texts": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "ScaleValue_texts", "@EntityRelationship.entityType": "sap.sf.performance:scaleValue_texts", "elements": {"scale_values_scaleId_virtual": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_scale_values"}, "type": "cds.Decimal", "key": true}, "_scale_values": {"type": "cds.Association", "target": "scale_values", "cardinality": {"max": 1}, "on": [{"ref": ["_scale_values", "scaleId"]}, "=", {"ref": ["scale_values_scaleId_virtual"]}]}, "shortName": {"type": "cds.String", "@EndUserText.label": "ShortName"}, "longName": {"type": "cds.String", "@EndUserText.label": "LongName"}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale"}, "isDefault": {"type": "cds.String", "@EndUserText.label": "<PERSON><PERSON><PERSON><PERSON>"}}}}}