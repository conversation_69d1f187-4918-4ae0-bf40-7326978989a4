{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/routing/v1/metadata/routeMap", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "RouteMap", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.performance", "document": {"version": "1.0.0"}}, "definitions": {"RouteMap": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "route map resource", "@EntityRelationship.entityType": "sap.sf.performance:RouteMap", "@EntityRelationship.entityIds": [{"name": "RouteMap", "propertyTypes": ["sap.sf.performance:RouteMapId"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Route Map ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:RouteMapId"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 256}, "status": {"type": "cds.Integer64", "@EndUserText.label": "Status"}, "type": {"type": "cds.Integer64", "@EndUserText.label": "Type"}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 4000}, "version": {"type": "cds.Integer64", "@EndUserText.label": "Version"}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Time"}}}}}