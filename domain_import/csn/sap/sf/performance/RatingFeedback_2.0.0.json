{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/rating/v2/metadata/feedback", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "RatingFeedback", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.performance", "document": {"version": "2.0.0"}}, "definitions": {"RatingFeedback": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "feedback resource", "@EntityRelationship.entityType": "sap.sf.performance:RatingFeedback", "@EntityRelationship.entityIds": [{"name": "RatingFeedback", "propertyTypes": ["sap.sf.performance:RatingFeedbackId"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Feedback ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:RatingFeedbackId"}, "source": {"type": "cds.Integer64", "@EndUserText.label": "Source"}, "module": {"type": "cds.Integer64", "@EndUserText.label": "<PERSON><PERSON><PERSON>"}, "userId": {"type": "cds.String", "@EndUserText.label": "User ID", "length": 100}, "validFrom": {"type": "cds.DateTime", "@EndUserText.label": "<PERSON><PERSON>"}, "validTo": {"type": "cds.DateTime", "@EndUserText.label": "<PERSON><PERSON>"}, "formId": {"type": "cds.Integer64", "@EndUserText.label": "Form ID"}, "formContentId": {"type": "cds.Integer64", "@EndUserText.label": "Form Content ID"}, "type": {"type": "cds.Integer64", "@EndUserText.label": "Type"}, "itemId": {"type": "cds.Integer64", "@EndUserText.label": "Item ID"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 2000}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 4000}, "rating": {"type": "cds.Decimal", "@EndUserText.label": "Rating", "precision": 34, "scale": 1}, "weight": {"type": "cds.Decimal", "@EndUserText.label": "Weight", "precision": 34, "scale": 1}, "scaleId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.performance:RatingScale", "referencedPropertyType": "sap.sf.performance:RatingScaleId"}], "type": "cds.Integer64", "@EndUserText.label": "Scale ID"}, "scaleMin": {"type": "cds.Decimal", "@EndUserText.label": "Scale Min", "precision": 34, "scale": 1}, "scaleMax": {"type": "cds.Decimal", "@EndUserText.label": "Scale Max", "precision": 34, "scale": 1}, "raterId": {"type": "cds.String", "@EndUserText.label": "Rater ID", "length": 100}, "raterCategory": {"type": "cds.String", "@EndUserText.label": "Rater Category", "length": 256}, "ratingLabel": {"type": "cds.String", "@EndUserText.label": "Rating Label", "length": 128}, "expectedRating": {"type": "cds.Decimal", "@EndUserText.label": "Expected Rating", "precision": 34, "scale": 1}, "attachmentId": {"type": "cds.Integer64", "@EndUserText.label": "Attachment ID"}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Time"}}}}}