{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/goals/v1/metadata/objective", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "Objective", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.performance", "document": {"version": "1.0.0"}}, "definitions": {"Objective": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Objective", "@ODM.entityName": "PerformanceObjective", "@ODM.oid": "id", "@EntityRelationship.entityType": "sap.sf.performance:Objective", "@EntityRelationship.entityIds": [{"name": "Objective", "propertyTypes": ["sap.sf.performance:ObjectiveId"]}], "elements": {"genaiFieldIds": {"type": "cds.String", "@EndUserText.label": "AI-Generated Field IDs"}, "lastStatusItemId": {"type": "cds.Integer64", "@EndUserText.label": "Last Status Item ID"}, "lastUpdateRequestId": {"type": "cds.Integer64", "@EndUserText.label": "Last Update Request ID"}, "achievement": {"type": "cds.Decimal", "@EndUserText.label": "Achievement", "precision": 34, "scale": 1}, "achievementText": {"type": "cds.String", "@EndUserText.label": "Achievement Text"}, "actual": {"type": "cds.Decimal", "@EndUserText.label": "Actual Value", "precision": 34, "scale": 1}, "category": {"type": "cds.String", "@EndUserText.label": "Category"}, "currentOwner": {"type": "cds.String", "@EndUserText.label": "Current Owner"}, "defgrp": {"type": "cds.String", "@EndUserText.label": "Default Group"}, "dept": {"type": "cds.String", "@EndUserText.label": "Department"}, "description": {"type": "cds.String", "@EndUserText.label": "Description"}, "div": {"type": "cds.String", "@EndUserText.label": "Division"}, "dueDate": {"type": "cds.String", "@EndUserText.label": "Due Date"}, "flag": {"type": "cds.Integer64", "@EndUserText.label": "Flag"}, "objectiveAssociateId": {"type": "cds.String", "@EndUserText.label": "Objective Associate ID"}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Objective ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:ObjectiveId"}, "modifiedAt": {"type": "cds.String", "@EndUserText.label": "Modified At"}, "loc": {"type": "cds.String", "@EndUserText.label": "Location"}, "masterId": {"type": "cds.Integer64", "@EndUserText.label": "Master ID"}, "metric": {"type": "cds.String", "@EndUserText.label": "Metric"}, "mltAchievementType": {"type": "cds.Integer64", "@EndUserText.label": "MLT Achievement Type"}, "mltScale": {"type": "cds.String", "@EndUserText.label": "MLT Scale"}, "modifier": {"type": "cds.String", "@EndUserText.label": "Modifier"}, "name": {"type": "cds.String", "@EndUserText.label": "Name"}, "numbering": {"type": "cds.String", "@EndUserText.label": "Numbering"}, "parentId": {"type": "cds.Integer64", "@EndUserText.label": "Parent ID"}, "percent": {"type": "cds.Decimal", "@EndUserText.label": "Percent", "precision": 34, "scale": 1}, "periodEnd": {"type": "cds.String", "@EndUserText.label": "Period End"}, "periodStart": {"type": "cds.String", "@EndUserText.label": "Period Start"}, "pos": {"type": "cds.Integer64", "@EndUserText.label": "Position"}, "rating": {"type": "cds.Decimal", "@EndUserText.label": "Rating", "precision": 34, "scale": 1}, "state": {"type": "cds.String", "@EndUserText.label": "State"}, "status": {"type": "cds.Integer64", "@EndUserText.label": "Status"}, "strategic": {"type": "cds.Integer64", "@EndUserText.label": "Strategic"}, "target": {"type": "cds.Decimal", "@EndUserText.label": "Target", "precision": 34, "scale": 1}, "type": {"type": "cds.String", "@EndUserText.label": "Type"}, "weight": {"type": "cds.Decimal", "@EndUserText.label": "Weight", "precision": 34, "scale": 1}, "originalId": {"type": "cds.Integer64", "@EndUserText.label": "Original ID"}, "userId": {"type": "cds.String", "@EndUserText.label": "User ID"}, "createdBy": {"type": "cds.String", "@EndUserText.label": "Created By"}, "lastModifiedBy": {"type": "cds.String", "@EndUserText.label": "Last Modified By"}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created At"}, "lastModifiedTime": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Time"}, "templateId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.performance:ObjectiveTemplate", "referencedPropertyType": "sap.sf.performance:ObjectiveTemplateId"}], "type": "cds.Integer64", "@EndUserText.label": "Template ID"}, "planState": {"type": "cds.String", "@EndUserText.label": "Plan State"}, "milestones": {"type": "cds.Composition", "target": "ObjectiveMileStone", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Milestones", "on": [{"ref": ["milestones", "_id"]}, "=", {"ref": ["id"]}]}, "metricLookups": {"type": "cds.Composition", "target": "ObjectiveMetricLookup", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Metric Lookups", "on": [{"ref": ["metricLookups", "_id"]}, "=", {"ref": ["id"]}]}}}, "ObjectiveMileStone": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Objective Milestone", "@EntityRelationship.entityType": "sap.sf.performance:ObjectiveMileStone", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Objective"}, "type": "cds.Integer64", "key": true}, "_Objective": {"type": "cds.Association", "target": "Objective", "cardinality": {"max": 1}, "on": [{"ref": ["_Objective", "id"]}, "=", {"ref": ["_id"]}]}, "objectiveId": {"type": "cds.Integer64", "@EndUserText.label": "Objective ID"}, "actual": {"type": "cds.String", "@EndUserText.label": "Actual Value"}, "actualNumber": {"type": "cds.Decimal", "@EndUserText.label": "Actual Number", "precision": 34, "scale": 1}, "code": {"type": "cds.String", "@EndUserText.label": "Code"}, "customNum1": {"type": "cds.Decimal", "@EndUserText.label": "Custom Number 1", "precision": 34, "scale": 1}, "customNum2": {"type": "cds.Decimal", "@EndUserText.label": "Custom Number 2", "precision": 34, "scale": 1}, "customNum3": {"type": "cds.Decimal", "@EndUserText.label": "Custom Number 3", "precision": 34, "scale": 1}, "desc": {"type": "cds.String", "@EndUserText.label": "Description"}, "displayOrder": {"type": "cds.Integer64", "@EndUserText.label": "Display Order"}, "due": {"type": "cds.String", "@EndUserText.label": "Due Date"}, "flag": {"type": "cds.Integer64", "@EndUserText.label": "Flag"}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Milestone ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:ObjectiveMileStoneId"}, "lastmodified": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified"}, "masterId": {"type": "cds.Integer64", "@EndUserText.label": "Master ID"}, "modifier": {"type": "cds.String", "@EndUserText.label": "Modifier"}, "percent": {"type": "cds.Decimal", "@EndUserText.label": "Percent", "precision": 34, "scale": 1}, "rating": {"type": "cds.Decimal", "@EndUserText.label": "Rating", "precision": 34, "scale": 1}, "score": {"type": "cds.Decimal", "@EndUserText.label": "Score", "precision": 34, "scale": 1}, "start": {"type": "cds.String", "@EndUserText.label": "Start Date"}, "target": {"type": "cds.String", "@EndUserText.label": "Target"}, "weight": {"type": "cds.Decimal", "@EndUserText.label": "Weight", "precision": 34, "scale": 1}}}, "ObjectiveMetricLookup": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Objective Metric Lookup", "@EntityRelationship.entityType": "sap.sf.performance:ObjectiveMetricLookup", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_Objective"}, "type": "cds.Integer64", "key": true}, "_Objective": {"type": "cds.Association", "target": "Objective", "cardinality": {"max": 1}, "on": [{"ref": ["_Objective", "id"]}, "=", {"ref": ["_id"]}]}, "achievementNumeric": {"type": "cds.Decimal", "@EndUserText.label": "Achievement Numeric", "precision": 34, "scale": 1}, "achievementText": {"type": "cds.String", "@EndUserText.label": "Achievement Text"}, "code": {"type": "cds.String", "@EndUserText.label": "Code"}, "description": {"type": "cds.String", "@EndUserText.label": "Description"}, "lastModified": {"type": "cds.String", "@EndUserText.label": "Last Modified"}, "objectiveId": {"type": "cds.Integer64", "@EndUserText.label": "Objective ID"}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Metric Lookup ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:ObjectiveMetricLookupId"}, "objMetriclookupIndex": {"type": "cds.Integer64", "@EndUserText.label": "Metric Lookup Index"}, "objMetriclookupMasterId": {"type": "cds.Integer64", "@EndUserText.label": "Metric Lookup Master ID"}, "rating": {"type": "cds.Decimal", "@EndUserText.label": "Rating", "precision": 34, "scale": 1}}}}}