{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/goals/v1/metadata/template", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "ObjectiveTemplate", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.performance", "document": {"version": "1.0.0"}}, "definitions": {"ObjectiveTemplate": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "Objective Template", "@ODM.entityName": "PerformanceObjectiveTemplate", "@ODM.oid": "id", "@EntityRelationship.entityType": "sap.sf.performance:ObjectiveTemplate", "@EntityRelationship.entityIds": [{"name": "ObjectiveTemplate", "propertyTypes": ["sap.sf.performance:ObjectiveTemplateId"]}], "elements": {"defaultPlanState": {"type": "cds.String", "@EndUserText.label": "Default Plan State"}, "displayOrder": {"type": "cds.Integer64", "@EndUserText.label": "Display Order"}, "dueDate": {"type": "cds.DateTime", "@EndUserText.label": "Due Date"}, "backupCopyId": {"type": "cds.Integer64", "@EndUserText.label": "Backup Copy ID"}, "desc": {"type": "cds.String", "@EndUserText.label": "Description"}, "id": {"type": "cds.Integer64", "@EndUserText.label": "Objective Template ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:ObjectiveTemplateId", "@ObjectModel.text.association": {"=": "texts"}}, "lastmodified": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified"}, "name": {"type": "cds.String", "@EndUserText.label": "Name"}, "version": {"type": "cds.String", "@EndUserText.label": "Version"}, "parentId": {"type": "cds.Integer64", "@EndUserText.label": "Parent Plan ID"}, "startDate": {"type": "cds.DateTime", "@EndUserText.label": "Start Date"}, "status": {"type": "cds.Integer64", "@EndUserText.label": "Status"}, "type": {"type": "cds.Integer64", "@EndUserText.label": "Type"}, "minGoals": {"type": "cds.Integer64", "@EndUserText.label": "Min Goal"}, "maxGoals": {"type": "cds.Integer64", "@EndUserText.label": "Max Goal"}, "createdBy": {"type": "cds.String", "@EndUserText.label": "Created By"}, "lastModifiedBy": {"type": "cds.String", "@EndUserText.label": "Last Modified By"}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created At"}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Modified At"}, "texts": {"type": "cds.Composition", "target": "TemplateText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_id"]}, "=", {"ref": ["id"]}]}, "category": {"type": "cds.Composition", "target": "TemplateCategory", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Category", "on": [{"ref": ["category", "_id"]}, "=", {"ref": ["id"]}]}, "state": {"type": "cds.Composition", "target": "TemplateState", "cardinality": {"max": 1}, "@EndUserText.label": "State", "on": [{"ref": ["state", "_id"]}, "=", {"ref": ["id"]}]}}}, "TemplateText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Objective Template Texts", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.performance:TemplateText", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_ObjectiveTemplate"}, "type": "cds.Integer64", "key": true}, "_ObjectiveTemplate": {"type": "cds.Association", "target": "ObjectiveTemplate", "cardinality": {"max": 1}, "on": [{"ref": ["_ObjectiveTemplate", "id"]}, "=", {"ref": ["_id"]}]}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "@Semantics.text": true}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:TemplateTextLocale", "@Semantics.language": true}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "<PERSON><PERSON><PERSON>"}}}, "TemplateCategory": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Objective Template Category", "@EntityRelationship.entityType": "sap.sf.performance:TemplateCategory", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_ObjectiveTemplate"}, "type": "cds.Integer64", "key": true}, "_ObjectiveTemplate": {"type": "cds.Association", "target": "ObjectiveTemplate", "cardinality": {"max": 1}, "on": [{"ref": ["_ObjectiveTemplate", "id"]}, "=", {"ref": ["_id"]}]}, "id": {"type": "cds.String", "@EndUserText.label": "ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:TemplateCategoryId", "@ObjectModel.text.association": {"=": "texts"}}, "templateId": {"type": "cds.Integer64", "@EndUserText.label": "Template ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:TemplateCategoryTemplateId", "@ObjectModel.text.association": {"=": "texts"}}, "name": {"type": "cds.String", "@EndUserText.label": "Category name"}, "texts": {"type": "cds.Composition", "target": "TemplateCategoryText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_id"]}, "=", {"ref": ["id"]}, "and", {"ref": ["texts", "_templateId"]}, "=", {"ref": ["templateId"]}]}}}, "TemplateState": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Objective Template State Enum Value", "@EntityRelationship.entityType": "sap.sf.performance:TemplateState", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_ObjectiveTemplate"}, "type": "cds.Integer64", "key": true}, "_ObjectiveTemplate": {"type": "cds.Association", "target": "ObjectiveTemplate", "cardinality": {"max": 1}, "on": [{"ref": ["_ObjectiveTemplate", "id"]}, "=", {"ref": ["_id"]}]}, "label": {"type": "cds.String", "@EndUserText.label": "Label", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:TemplateStateLabel"}, "templateId": {"type": "cds.Integer64", "@EndUserText.label": "Template ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:TemplateStateTemplateId"}, "values": {"type": "cds.Composition", "target": "TemplateStateEnumValue", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Status value", "on": [{"ref": ["values", "_label"]}, "=", {"ref": ["label"]}, "and", {"ref": ["values", "_templateId"]}, "=", {"ref": ["templateId"]}]}}}, "TemplateCategoryText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Objective Template Texts", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.performance:TemplateCategoryText", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_TemplateCategory"}, "type": "cds.Integer64"}, "_TemplateCategory": {"type": "cds.Association", "target": "TemplateCategory", "cardinality": {"max": 1}, "on": [{"ref": ["_TemplateCategory", "templateId"]}, "=", {"ref": ["_templateId"]}]}, "_templateId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_TemplateCategory"}, "type": "cds.Integer64", "key": true}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "@Semantics.text": true}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:TemplateCategoryTextLocale", "@Semantics.language": true}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "<PERSON><PERSON><PERSON>"}}}, "TemplateStateEnumValue": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Objective Template State Enum Value", "@EntityRelationship.entityType": "sap.sf.performance:TemplateStateEnumValue", "elements": {"_label": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_TemplateState"}, "type": "cds.Integer64"}, "_TemplateState": {"type": "cds.Association", "target": "TemplateState", "cardinality": {"max": 1}, "on": [{"ref": ["_TemplateState", "templateId"]}, "=", {"ref": ["_templateId"]}]}, "_templateId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_TemplateState"}, "type": "cds.Integer64", "key": true}, "value": {"type": "cds.String", "@EndUserText.label": "Value", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:TemplateStateEnumValueValue", "@ObjectModel.text.association": {"=": "texts"}}, "templateId": {"type": "cds.Integer64", "@EndUserText.label": "Template ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:TemplateStateEnumValueTemplateId", "@ObjectModel.text.association": {"=": "texts"}}, "label": {"type": "cds.String", "@EndUserText.label": "State label"}, "completionTrigger": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Completion Trigger"}, "texts": {"type": "cds.Composition", "target": "TemplateStateEnumValueText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Localized Texts", "on": [{"ref": ["texts", "_templateId"]}, "=", {"ref": ["templateId"]}, "and", {"ref": ["texts", "_value"]}, "=", {"ref": ["value"]}]}}}, "TemplateStateEnumValueText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Objective Template State Enum Value Texts", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.performance:TemplateStateEnumValueText", "elements": {"_templateId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_TemplateStateEnumValue"}, "type": "cds.Integer64"}, "_TemplateStateEnumValue": {"type": "cds.Association", "target": "TemplateStateEnumValue", "cardinality": {"max": 1}, "on": [{"ref": ["_TemplateStateEnumValue", "value"]}, "=", {"ref": ["_value"]}]}, "_value": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_TemplateStateEnumValue"}, "type": "cds.Integer64", "key": true}, "label": {"type": "cds.String", "@EndUserText.label": "Label", "@Semantics.text": true}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:TemplateStateEnumValueTextLocale", "@Semantics.language": true}, "default": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "<PERSON><PERSON><PERSON>"}}}}}