{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/performance/review/v1/metadata/auditTrail", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "AuditTrail", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.performance", "document": {"version": "1.0.0"}}, "definitions": {"auditTrail": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "scale resource", "@EntityRelationship.entityType": "sap.sf.performance:AuditTrail", "@EntityRelationship.entityIds": [{"name": "AuditTrail", "propertyTypes": ["sap.sf.performance:AuditTrailUUID"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Audit Trail ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:AuditTrailUUID"}, "formId": {"type": "cds.Integer64", "@EndUserText.label": "Form ID"}, "formContentId": {"type": "cds.Integer64", "@EndUserText.label": "Form Content ID"}, "action": {"type": "cds.String", "@EndUserText.label": "Action", "length": 16}, "createdBy": {"type": "cds.String", "@EndUserText.label": "Created By", "length": 100}, "recipient": {"type": "cds.String", "@EndUserText.label": "Recipient", "length": 100}, "comment": {"type": "cds.String", "@EndUserText.label": "Comment", "length": 2048}, "sendProxy": {"type": "cds.String", "@EndUserText.label": "Send Proxy", "length": 100}, "coSender": {"type": "cds.String", "@EndUserText.label": "Co-Sender", "length": 1024}, "formContentAssociatedStepId": {"type": "cds.String", "@EndUserText.label": "Form Content Associated Step ID", "length": 128}, "sendDelegatee": {"type": "cds.String", "@EndUserText.label": "Send Delegatee", "length": 100}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Time"}}}}}