{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/rating/v2/metadata/scale", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "RatingScale", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.performance", "document": {"version": "2.0.0"}}, "definitions": {"RatingScale": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "scale resource", "@EntityRelationship.entityType": "sap.sf.performance:RatingScale", "@EntityRelationship.entityIds": [{"name": "RatingScale", "propertyTypes": ["sap.sf.performance:RatingScaleId"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Scale ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:RatingScaleId"}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "length": 32}, "source": {"type": "cds.Integer64", "@EndUserText.label": "Source"}, "type": {"type": "cds.String", "@EndUserText.label": "Type", "length": 32}, "status": {"type": "cds.Integer64", "@EndUserText.label": "Status"}, "defaultLocale": {"type": "cds.String", "@EndUserText.label": "Default Locale", "length": 32}, "description": {"type": "cds.String", "@EndUserText.label": "Description", "length": 4000}, "modifiedBy": {"type": "cds.String", "@EndUserText.label": "Last Modified By", "length": 100}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created Date Time"}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Last Modified Time"}, "values": {"type": "cds.Composition", "target": "ScaleValue", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Values", "on": [{"ref": ["values", "_id"]}, "=", {"ref": ["id"]}]}}}, "ScaleValue": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "scale value resource", "@EntityRelationship.entityType": "sap.sf.performance:ScaleValue", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_RatingScale"}, "type": "cds.Integer64", "key": true}, "_RatingScale": {"type": "cds.Association", "target": "RatingScale", "cardinality": {"max": 1}, "on": [{"ref": ["_RatingScale", "id"]}, "=", {"ref": ["_id"]}]}, "scaleId": {"type": "cds.Integer64", "@EndUserText.label": "Scale ID", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:ScaleValueScaleId", "@ObjectModel.text.association": {"=": "texts"}}, "value": {"type": "cds.String", "@EndUserText.label": "Value", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:ScaleValueValue", "@ObjectModel.text.association": {"=": "texts"}}, "maxValue": {"type": "cds.String", "@EndUserText.label": "Max Value"}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "length": 32}, "shortName": {"type": "cds.String", "@EndUserText.label": "Short Name", "length": 128}, "longName": {"type": "cds.String", "@EndUserText.label": "Long Name", "length": 4000}, "imageId": {"type": "cds.Integer64", "@EndUserText.label": "Image ID"}, "texts": {"type": "cds.Composition", "target": "ScaleValueLocalizedText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Texts", "on": [{"ref": ["texts", "_scaleId"]}, "=", {"ref": ["scaleId"]}, "and", {"ref": ["texts", "_value"]}, "=", {"ref": ["value"]}]}}}, "ScaleValueLocalizedText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "ScaleValueLocalizedText", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.performance:ScaleValueLocalizedText", "elements": {"_scaleId": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_ScaleValue"}, "type": "cds.String", "@Semantics.text": true}, "_ScaleValue": {"type": "cds.Association", "target": "ScaleValue", "cardinality": {"max": 1}, "on": [{"ref": ["_ScaleValue", "value"]}, "=", {"ref": ["_value"]}]}, "_value": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_ScaleValue"}, "type": "cds.String", "key": true, "@Semantics.text": true}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:ScaleValueLocalizedTextLocale", "@Semantics.language": true}, "isDefault": {"type": "cds.String", "@EndUserText.label": "<PERSON><PERSON><PERSON><PERSON>", "@Semantics.text": true}, "shortName": {"type": "cds.String", "@EndUserText.label": "ShortName", "@Semantics.text": true}, "longName": {"type": "cds.String", "@EndUserText.label": "LongName", "@Semantics.text": true}}}}}