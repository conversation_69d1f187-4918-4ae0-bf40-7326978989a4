{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/performance/review/v2/metadata/formTemplate", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "FormTemplate", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.performance", "document": {"version": "2.0.0"}}, "definitions": {"FormTemplate": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "form template resource", "@EntityRelationship.entityType": "sap.sf.performance:FormTemplate", "@EntityRelationship.entityIds": [{"name": "FormTemplate", "propertyTypes": ["sap.sf.performance:FormTemplateId"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Template Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:FormTemplateId", "@ObjectModel.text.association": {"=": "texts"}}, "type": {"type": "cds.String", "@EndUserText.label": "Template Type", "length": 32}, "status": {"type": "cds.Decimal", "@EndUserText.label": "Template Status", "precision": 34, "scale": 1}, "name": {"type": "cds.String", "@EndUserText.label": "Template Name", "length": 128}, "startDate": {"type": "cds.DateTime", "@EndUserText.label": "Template Start Date"}, "endDate": {"type": "cds.DateTime", "@EndUserText.label": "Template End Date"}, "effectiveDate": {"type": "cds.DateTime", "@EndUserText.label": "Template Effective Date"}, "version": {"type": "cds.Integer64", "@EndUserText.label": "Template Version"}, "routeId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.performance:RouteMap", "referencedPropertyType": "sap.sf.performance:RouteMapId"}], "type": "cds.Integer64", "@EndUserText.label": "Template Route Id"}, "description": {"type": "cds.String", "@EndUserText.label": "Template Description", "length": 1024}, "parentId": {"type": "cds.Integer64", "@EndUserText.label": "Template Parent Id"}, "subType": {"type": "cds.Integer64", "@EndUserText.label": "Template Sub Type"}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Template Last Modified Time"}, "texts": {"type": "cds.Composition", "target": "FormTemplateLocalizedText", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Texts", "on": [{"ref": ["texts", "_id"]}, "=", {"ref": ["id"]}]}}}, "FormTemplateLocalizedText": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "FormTemplateLocalizedText", "@ObjectModel.modelingPattern": {"#": "LANGUAGE_DEPENDENT_TEXT"}, "@EntityRelationship.entityType": "sap.sf.performance:FormTemplateLocalizedText", "elements": {"_id": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_FormTemplate"}, "type": "cds.Integer64", "key": true}, "_FormTemplate": {"type": "cds.Association", "target": "FormTemplate", "cardinality": {"max": 1}, "on": [{"ref": ["_FormTemplate", "id"]}, "=", {"ref": ["_id"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.performance:FormTemplateLocalizedTextLocale", "@Semantics.language": true}, "isDefault": {"type": "cds.String", "@EndUserText.label": "<PERSON><PERSON><PERSON><PERSON>", "@Semantics.text": true}, "name": {"type": "cds.String", "@EndUserText.label": "Name", "@Semantics.text": true}}}}}