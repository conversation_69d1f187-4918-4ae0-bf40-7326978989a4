{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/succession/v1/metadata/readiness", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "Readiness", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.career", "document": {"version": "1.0.0"}}, "definitions": {"readiness": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "readiness metadata", "@EntityRelationship.entityType": "sap.sf.career:Readiness", "@EntityRelationship.entityIds": [{"name": "Readiness", "propertyTypes": ["sap.sf.career:ReadinessID"]}], "elements": {"id": {"type": "cds.Decimal", "@EndUserText.label": "Readiness Id", "precision": 34, "scale": 1, "key": true, "@EntityRelationship.propertyType": "sap.sf.career:ReadinessID"}, "name": {"type": "cds.String", "@EndUserText.label": "Name"}, "index": {"type": "cds.Integer64", "@EndUserText.label": "Index"}, "texts": {"type": "cds.Composition", "target": "readiness_texts", "cardinality": {"src": 1, "max": "*"}, "@EndUserText.label": "Readiness Localized Texts", "on": [{"ref": ["texts", "readiness_id_virtual"]}, "=", {"ref": ["id"]}]}}}, "readiness_texts": {"kind": "entity", "@ObjectModel.compositionRoot": false, "@EndUserText.label": "Readiness_texts", "@EntityRelationship.entityType": "sap.sf.career:readiness_texts", "elements": {"readiness_id_virtual": {"@virtual": true, "@ObjectModel.foreignKey.association": {"=": "_readiness"}, "type": "cds.Decimal", "key": true}, "_readiness": {"type": "cds.Association", "target": "readiness", "cardinality": {"max": 1}, "on": [{"ref": ["_readiness", "id"]}, "=", {"ref": ["readiness_id_virtual"]}]}, "locale": {"type": "cds.String", "@EndUserText.label": "Locale", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:readiness_textsLocale"}, "name": {"type": "cds.String", "@EndUserText.label": "Name"}}}}}