{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/succession/v1/metadata/talentPoolNomination", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "TalentPoolNomination", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.career", "document": {"version": "1.0.0"}}, "definitions": {"talentPoolNomination": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "talent pool nomination metadata", "@EntityRelationship.entityType": "sap.sf.career:TalentPoolNomination", "@EntityRelationship.entityIds": [{"name": "TalentPoolNomination", "propertyTypes": ["sap.sf.career:TalentPoolNominationID"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Talent Pool Nomination Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:TalentPoolNominationID"}, "nominationTypeId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.career:nominationType", "referencedPropertyType": "sap.sf.career:nominationTypeId"}], "type": "cds.Integer64", "@EndUserText.label": "Nomination Type Id"}, "poolId": {"type": "cds.Integer64", "@EndUserText.label": "Talent Pool Id"}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created At"}, "createdBy": {"type": "cds.String", "@EndUserText.label": "Created By"}}}}}