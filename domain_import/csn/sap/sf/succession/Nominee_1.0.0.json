{"$schema": "https://sap.github.io/csn-interop-specification/spec-v1/csn-interop-effective.schema.json", "$id": "/rest/talent/succession/v1/metadata/nominee", "csnInteropEffective": "1.0", "$version": "2.0", "meta": {"__name": "<PERSON><PERSON><PERSON>", "creator": "Successfactors CSN Object Model API v1.0.0-alpha", "__modelProvider": "Successfactors", "__namespace": "sap.sf.career", "document": {"version": "1.0.0"}}, "definitions": {"nominee": {"kind": "entity", "@ObjectModel.compositionRoot": true, "@EndUserText.label": "nominee metadata", "@EntityRelationship.entityType": "sap.sf.career:<PERSON><PERSON><PERSON>", "@EntityRelationship.entityIds": [{"name": "<PERSON><PERSON><PERSON>", "propertyTypes": ["sap.sf.career:<PERSON><PERSON>e<PERSON>"]}], "elements": {"id": {"type": "cds.Integer64", "@EndUserText.label": "Nominee Id", "key": true, "@EntityRelationship.propertyType": "sap.sf.career:<PERSON><PERSON>e<PERSON>"}, "nominationId": {"type": "cds.Integer64", "@EndUserText.label": "Nomination Id"}, "nomineeUserId": {"type": "cds.String", "@EndUserText.label": "Nominee User Id"}, "readiness": {"type": "cds.Decimal", "@EndUserText.label": "Readiness", "precision": 34, "scale": 1}, "note": {"type": "cds.String", "@EndUserText.label": "Note"}, "nomineeStatusId": {"@EntityRelationship.reference": [{"referencedEntityType": "sap.sf.career:nominee<PERSON><PERSON><PERSON>", "referencedPropertyType": "sap.sf.career:nomineeStatusId"}], "type": "cds.Integer64", "@EndUserText.label": "Nominee Status Id"}, "proxyUserId": {"type": "cds.String", "@EndUserText.label": "Proxy User Id"}, "rank": {"type": "cds.Decimal", "@EndUserText.label": "Rank", "precision": 34, "scale": 1}, "externalCandId": {"type": "cds.Integer64", "@EndUserText.label": "External Candidate Id"}, "emergencyCover": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Emergency Cover"}, "modifiedAt": {"type": "cds.DateTime", "@EndUserText.label": "Modified At"}, "modifiedBy": {"type": "cds.String", "@EndUserText.label": "Modified By"}, "createdAt": {"type": "cds.DateTime", "@EndUserText.label": "Created At"}, "createdBy": {"type": "cds.String", "@EndUserText.label": "Created By"}, "deleted": {"type": "cds.<PERSON><PERSON><PERSON>", "@EndUserText.label": "Deleted"}}}}}