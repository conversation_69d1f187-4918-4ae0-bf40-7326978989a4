# DemoGenerator

Repository for HCM dataproducts definition files

## Actions Status

[![[LOB] CSN Import --> Collibra Domain Objects and Nodes](https://github.tools.sap/bdc/DemoGenerator/actions/workflows/csn_import.yml/badge.svg?branch=main)](https://github.tools.sap/bdc/DemoGenerator/actions/workflows/csn_import.yml)
[![[LOB] Intermedate DPD Generation](https://github.tools.sap/bdc/DemoGenerator/actions/workflows/dpd_gen.yml/badge.svg)](https://github.tools.sap/bdc/DemoGenerator/actions/workflows/dpd_gen.yml)
[![Package Gen Workflow](https://github.tools.sap/bdc/DemoGenerator/actions/workflows/pkg_gen.yml/badge.svg)](https://github.tools.sap/bdc/DemoGenerator/actions/workflows/pkg_gen.yml)
[![FOS PR Creation](https://github.tools.sap/bdc/DemoGenerator/actions/workflows/fos_pr.yml/badge.svg)](https://github.tools.sap/bdc/DemoGenerator/actions/workflows/fos_pr.yml)


## Steps to Generate DPD Files :

1. **Push csn metadata to domain_import**

> ⚠️ **Important:**
> Ensure that the CSN metadata files are pushed to the specified folder structure corresponding to the metadata events namespace.
> For example:
> `domain_import/csn/sap/lvl1/lvl2/.jsonfiles>` (e.g., `sap.lvl1.lvl2:entityname`)

### 📂 Folder Structure Example

```markdown

Here is an example of the folder structure for CSN metadata files:

```markdown
📁 domain_import
 └── 📁 csn
    └── 📁 sap
        └── 📁 lvl1
           └── 📁 lvl2
              └── 📄 DataProductConfig_1.0.0.json
```


> ⚠️ **Important:**
> Ensure that the `__name` property and `version` property inside your CSN metadata file match the file name format `__name_version.json`.
> If this naming convention is not followed, the process will fail during validation in the FOS `dp-metadata`.

### 📄 File Name Example:

For a file named DataProductConfig_1.0.0.json, which is derived from the meta object within the CSN file

```json
"meta":{
    "__name": "DataProductConfig",
    "document": {
        "version": "1.0.0"
    }
}
```

2. **📥 Planning Excel Import or Create Data Product Definitions via Collibra DP Repository Workflow**

    - 📝 One-time import of the mass planning Excel file is supported.

3. **⚙️ Generate DPD Files**

    - 🛠️ Trigger GitHub Actions to generate DPD and FOS files on-demand.
    - ✅ Validations performed include:
        1. Validation of CSN files against the API metadata validator.
        2. Validation of FOS files against the FOS v2 schema.

4. **📤 Fos PR Creation**

    - 🔗 Create a Pull Request (PR) to FOS `dp-metadata`.
    - 🎉 Enables smooth onboarding of Data Products in the FOS Workbench.

