from collibra_lib import CollibraAP<PERSON>, log, COLORS
from consts import *
import os
import json
import sys
col = CollibraAPI()

dpvs = []
ROOT = "fos_dpd/"
dct = {}


class DpRelease:
    """Class to handle the release of data products."""
    
    def __init__(self):
        self.dpvs = dpvs

    def get_attribute_types(self,asset_id, publicid):
        """Fetch attribute values for assets."""
        try:
            response = col.call(url_path='attributes', parameters={"assetId": asset_id, "typePublicIds": publicid})
            return response.get('results', [])
        except Exception as e:
            log(f"Error fetching attribute types: {e}", COLORS.RED)
            return []
        
    def get_info(self,):
        """Retrieve data product versions and populate dpvs."""
        try:
            packages_response = col.call(url_path='communities', parameters={'parentId': COMMUNITY_ID})
            packages = packages_response.get('results', [])
            for package in packages:
                split_name = package['name'].split(":")[0].split(".")
                domains_response = col.call(url_path='domains', parameters={'communityId': package['id']})
                domains = domains_response.get('results', [])
                for domain in domains:
                    domain_id = domain['id']
                    data_product_name = domain['name']
                    data_product_version_response = col.call(
                        url_path='assets',
                        parameters={'domainId': domain_id, 'typePublicIds': DATA_PRODUCT_VERSION}
                    )
                    data_product_versions = data_product_version_response.get('results', [])
                    for dpv in data_product_versions:
                        print(f"Data Product Version: {dpv['name']}")
                        self.process_data_product_version(dpv, split_name, data_product_name)
        except Exception as e:
            log(f"Error in get_info: {e}", COLORS.RED)

    def process_data_product_version(self,dpv, split_name, data_product_name):
        """Process individual data product version."""
        try:
            dpv_major_version = self.get_attribute_types(dpv['id'], MAJOR_VERSION)[0]['value']
            dpv_minor_version = self.get_attribute_types(dpv['id'], MINOR_VERSION)[0]['value']
            dpv_patch_version = self.get_attribute_types(dpv['id'], PATCH_VERSION)[0]['value']
            dpv_status = dpv['status']['name']
            if dpv_status == "Centrally Approved":
                dpvs.append(f"{'/'.join(split_name[1:3])}/{data_product_name}_{int(dpv_major_version)}.{int(dpv_minor_version)}.{int(dpv_patch_version)}")
        except Exception as e:
            log(f"Error processing data product version: {e}", COLORS.RED)

    def get_dp_paths(self,dpvs):
        """Resolve paths for data product versions."""
        for dpv in dpvs:
            dpv_file_name = f'{dpv.lower()}.json'
            for root, _, files in os.walk(os.path.join(ROOT, 'dataproducts')):
                for file in files:
                    if (root + '/' + file).lower().endswith(dpv_file_name):
                        self.process_data_product_file(root, file)
        
    def process_data_product_file(self,root, file):
        """Process individual data product file."""
        try:
            file_path = os.path.join(root, file)
            log(f"DATA PRODUCT: {file_path}", COLORS.GREEN)
            relative_path = file_path.replace(ROOT, "")
            dct[relative_path] = []
            with open(file_path, 'r') as f:
                data = json.load(f)
                if data.get('type') == 'derived':
                    self.process_derived_data_product(data, relative_path)
                else:
                    self.process_standard_data_product(data, relative_path)
        except Exception as e:
            log(f"Error processing data product file: {e}", COLORS.RED)

    def process_derived_data_product(self,data, relative_path):
        """Process derived data product."""
        transformers = data.get('derivedDataProductProperties', {}).get('transformers', [])
        csn_ids = data.get('derivedDataProductProperties', {}).get('metadata', {}).get('csnIds', [])
        shares = data.get('shares', [])
        dct[relative_path].append(self.get_csn_documents(csn_ids))
        dct[relative_path].append(self.get_transformer_paths(transformers))
        dct[relative_path].append(self.get_share_paths(shares))

    def process_standard_data_product(self,data, relative_path):
        """Process standard data product."""
        dependson = data.get('dependsOn', [])
        shares = data.get('shares', [])
        dct[relative_path].append(self.get_share_paths(shares))
        dct[relative_path].append(self.get_ds_paths(dependson, relative_path))


    
    def get_ds_paths(self,dependson, dpv_file_path):
        """Resolve paths for dependent data sources or datasets."""
        paths = []
        for ds in dependson:
            subdir = "datasources" if "dataSource" in ds else "datasets"
            sp = ds.split(":")
            version = ('.'.join(sp[-1].split("."))).replace('v', '_')
            for root, _, files in os.walk(os.path.join(ROOT, subdir, '/'.join(sp[0].split('.')))):
                for file in files:
                    if file.lower().endswith(f'{(sp[-2] + version).lower()}.json'):
                        paths.append(self.process_dependency_file(root, file, dpv_file_path, subdir))
        return paths

    def process_dependency_file(self,root, file, dpv_file_path, subdir):
        """Process individual dependency file."""
        try:
            file_path = os.path.join(root, file)
            relative_path = file_path.replace(ROOT, "")
            log(f" --- {subdir.upper()}: {file_path}", COLORS.YELLOW)
            if subdir == "datasources":
                csn_ids = json.load(open(file_path, 'r')).get('metadata', {}).get('csnIds', [])
                dct[dpv_file_path].append(self.get_csn_documents(csn_ids))
            elif subdir == "datasets":
                transformers = json.load(open(file_path, 'r')).get('transformers', [])
                dct[dpv_file_path].append(self.get_transformer_paths(transformers))
            return relative_path
        except Exception as e:
            log(f"Error processing dependency file: {e}", COLORS.RED)
            return ""

    def get_share_paths(self,shares):
        """Resolve paths for shared data products."""
        paths = []
        for share in shares:
            sp = share.split(":")
            version = ('.'.join(sp[-1].split("."))).replace('v', '_')
            for root, _, files in os.walk(os.path.join(ROOT, 'shares', '/'.join(sp[0].split('.')))):
                for file in files:
                    if file.lower().endswith(f'{(sp[-2] + version).lower()}.json'):
                        file_path = os.path.join(root, file)
                        paths.append(file_path.replace(ROOT, ""))
                        log(f" --- SHARE: {file_path}", COLORS.YELLOW)
        return paths

    def get_transformer_paths(self,transformers):
        """Resolve paths for transformers."""
        paths = []
        for transformer in transformers:
            sp = transformer['transformerName'].split(":")
            version = "_1.0.0"
            for root, _, files in os.walk(os.path.join(ROOT, 'transformers', '/'.join(sp[0].split('.')))):
                for file in files:
                    if file.lower().endswith(f'{(sp[-2] + version).lower()}.json'):
                        file_path = os.path.join(root, file)
                        paths.append(file_path.replace(ROOT, ""))
                        log(f" --- TRANSFORMER: {file_path}", COLORS.YELLOW)
        return paths

    def get_csn_documents(self,csn_ids):
        """Resolve paths for CSN documents."""
        paths = []
        for csn_document in csn_ids:
            sp = csn_document.split(":")
            version = ('.'.join(sp[-1].split("."))).replace('v', '_')
            for root, _, files in os.walk(os.path.join(ROOT, 'csn_documents', '/'.join(sp[0].split('.')))):
                for file in files:
                    if file.lower().endswith(f'{(sp[-2] + version).lower()}.json'):
                        file_path = os.path.join(root, file)
                        paths.append(file_path.replace(ROOT, ""))
                        log(f" --- CSN DOCUMENT: {file_path}", COLORS.YELLOW)
        return paths
    
    def print_results(self):
        """Print the resolved paths."""
        print("\n\nResolved Paths:")
        for key, groups in dct.items():
            print(key)
            for group in groups:
                for path in group:
                    print(f"  ├── {path}")
            print()

    def write_to_json(self):
        """Write the resolved paths to a JSON file."""
        with open('src/CollibraScripts/resolved_paths.json', 'w') as f:
            json.dump(dct, f, indent=4)
        log("Resolved paths written to resolved_paths.json", COLORS.GREEN)
        
    def move_to_stage(self):
        """Move the resolved paths file to the stage directory."""
        with open('src/CollibraScripts/resolved_paths.json', 'r') as f:
            data = json.load(f)

        stage_path = 'fos_dpd_prod/'

        for key, groups in data.items():
            dest_file_path = os.path.join(stage_path, key)
            dest_dir = os.path.dirname(dest_file_path)
            os.makedirs(dest_dir, exist_ok=True)
            os.system(f"cp {ROOT}/{key} {dest_file_path}")

            for group in groups:
                for path in group:
                    if path:
                        dest_path = os.path.join(stage_path, path)
                        os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                        os.system(f"cp {ROOT}/{path} {dest_path}")


if __name__ == "__main__":
    dp_release = DpRelease()
    if len(sys.argv) > 1 and sys.argv[1] == 'run':
        dp_release.get_info()
        dp_release.get_dp_paths(dpvs)
        # dp_release.print_results()
        dp_release.write_to_json()
        
    elif len(sys.argv) > 1 and sys.argv[1] == 'move':
        dp_release.move_to_stage()
    log("Execution completed successfully.", COLORS.GREEN)
    print("\n\n")
