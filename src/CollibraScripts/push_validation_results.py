import json
import os
import re
from collibra_lib import CollibraAPI, log, COLORS
from consts import *
col= CollibraAPI()
def parse_result_json(file_path):
    try:
        with open(file_path, 'r') as f:
            content = json.load(f)
        return content
    except FileNotFoundError:
        return f"File not found: {file_path}"
    except Exception as e:
        return f"Error reading file {file_path}: {str(e)}"
def search_assest_get_id(dataproduct_name):
    data_product_version_name = dataproduct_name + "V1"
    result = col.call(
        url_path='assets',
        parameters={
            'name': data_product_version_name,
            'typePublicIds': DATA_PRODUCT_VERSION,
            'nameMatchMode': "EXACT"
        } 
    )
    return result['results'][0]['id']
    
def extract_dp_name_from_path(file_path:str)->str:
    dp_pattern = r'hcm_dpd/DPDFiles/sap/sf/.*/([A-Za-z]+)_\d+\.\d+\.\d+\.json'
    match = re.search(dp_pattern, file_path)
    if match:
        return match.group(1)
    return ""
def get_attribute_error(dataproduct_uuid):
    asset_attrribute= col.call(
        url_path='attributes',
        parameters={
            'assetId': dataproduct_uuid
        }
    )
    for item in asset_attrribute['results']:
        if item['type']['name'] == 'DPD Error Log':
            attribute_id = item['id']
            existing_error = item['value']
            break 
        else:
            attribute_id = None
            existing_error = None
    return attribute_id, existing_error
def patch_error(attribute_id, ord_error, dataproduct_uuid:None):
    if attribute_id is None:
        col.call(
            url_path='attributes',
            method='POST',
            json={
                'assetId': dataproduct_uuid,
                'typeID':dataproduct_uuid,
                'typePublicId': DPD_ERROR_LOG,
                'value': ord_error
            }
        )
        return
    payload = {
        'value': ord_error  
    }
    col.call(
        url_path=f'attributes/{attribute_id}',  
        method='PATCH',
        json=payload
    )   
def main():
    list_assests=parse_result_json("./src/hcm-cpa-validation-result.json")
    for item in list_assests:
        dataproduct_name= extract_dp_name_from_path(item["path"])
        print(dataproduct_name)
        dp_id=search_assest_get_id(dataproduct_name)
        att_id,exist_error=get_attribute_error(dp_id)
        error_message =[message["message"] for message in item["messages"]]
        formatted_message=[]
        print("exist_error",exist_error)
        print("end")
        if exist_error:
            modified_error = re.sub(r'(\d+\.\s*)', '• ', exist_error)
            formatted_message.append(modified_error)
            for msg in error_message:
                if msg in exist_error:
                    continue
                format_message= f'<br/><span style="color :red;"> • {msg} </span><br/>'
                formatted_message.append(format_message)
        else:
            for msg in error_message:
                format_message= f'<br/><span style="color :red;"> • {msg} </span><br/>'
                formatted_message.append(format_message)
        formatted_message = " ".join(formatted_message)
        print(formatted_message)
        if att_id is None:
            patch_error(att_id,formatted_message,dp_id)
        else:
            patch_error(att_id,formatted_message, None)
if __name__=="__main__":
    main()
