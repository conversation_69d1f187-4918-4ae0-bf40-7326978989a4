import os
import json
import shutil
from collibra_lib import Collibra<PERSON><PERSON>, log, COLORS
from consts import *

col = CollibraAPI()

def extract_entities(csn_file):
    """
    DomainObject/datasource name should be extract from @EntityRelationship.entityType from root entity if annotations not present get the local entity name.
    All of the Nodes/Sharetables should be extracted from the local entity name definitions in the csn file.
    """

    with open(csn_file, 'r') as file:
        csn_data = json.load(file)
        meta_section = csn_data.get('meta',{})
        version = meta_section.get('document',{}).get('version')

    entities = csn_data.get('definitions', {})
    for entity_name,entity_properties in entities.items():
        if entity_properties.get('kind') == 'entity' and entity_properties.get('@ObjectModel.compositionRoot', True):
            local_csn_entity_name = entity_name
            rootentity = entity_properties
            break
   #Attributes for Domain Object
    entity_type = rootentity.get('@EntityRelationship.entityType','')
    if entity_type !='':
        domain_object = entity_type
    else:
        domain_object = local_csn_entity_name
    
    
    odm_entity = rootentity.get('@ODM.entityName','')
    if odm_entity:
        odm_entity_name = odm_entity
    else:
        odm_entity_name = domain_object
    
    title = rootentity.get('@EndUserText.label','')
    local_id = domain_object.split(':')[-1]
    namespace = domain_object.split(':')[0]
    ordid = f"{namespace}:entityType:{local_id}:v1"
    description = rootentity.get('@EndUserText.quickInfo','')
    short_description = rootentity.get('@EndUserText.heading','')
    major_version = version.split('.')[0]
    minor_version = version.split('.')[1] 
    patch_version = version.split('.')[2]



    nodes = []
    for entity_name, entity_properties in entities.items():

        # Check if the current entity is of kind 'entity'
        if entity_properties.get('kind') == 'entity': 
            is_root = entity_properties.get('@ObjectModel.compositionRoot', False) #get the root from the csn file 
            node_name  = f"{namespace}:{entity_name}" 
            node_local_id = node_name.split(':')[-1]
            node_title = entity_properties.get('@EndUserText.label','')
            node_description = entity_properties.get('@EndUserText.quickInfo','')
            node_short_description = entity_properties.get('@EndUserText.heading','') 
            nodes.append({
                'name': node_name,
                'is_root': str(is_root).capitalize(),
                'local_id': node_local_id,
                'title': node_title,
                'namespace': namespace,
                'ordid': f"{namespace}:entityType:{node_local_id}:v1",
                'description' : node_description,
                'short_description' : node_short_description
            }) #keep adding this when there is any other properties required from csn
    # Construct the payload
    payload = {domain_object: nodes} # nodes are extracted from the definition objects per entity kind in the csn file
    return payload,odm_entity_name,title,local_id,ordid,namespace,description,short_description, major_version, minor_version, patch_version

#Change the attributes of the domain object
def change_domain_object_attributes(id,local_id,title,ordid,namespace,description,short_description,major_version, minor_version, patch_version): 
    attributes = [{
        "typePublicId": LOCAL_ID,
        "values": [local_id],
    },
     {   "typePublicId": TITLE,
         "values": [title]
    },
    {   "typePublicId": ORD_ID,
         "values": [ordid]

    },
         {   "typePublicId": SHORT_DESCRIPTION,
         "values": [""]
    },
         {   "typePublicId": DESCRIPTION,
         "values": [""]
    },
         {   "typePublicId": NAMESPACE,
         "values": [namespace]
    },
             {   "typePublicId": DESCRIPTION,
          "values": [description]
    },
             {   "typePublicId": SHORT_DESCRIPTION,
         "values": [short_description]
    },
    {   "typePublicId": MAJOR_VERSION,
            "values": [major_version]
    }, 
    {   "typePublicId": MINOR_VERSION,
            "values": [minor_version]
    },
    {   "typePublicId": PATCH_VERSION,
            "values": [patch_version]
    }              
        ]
    for attribute in attributes:
        response = col.call(url_path=f'assets/{id}/attributes', json=attribute, method='put')



#change the attributes of the nodes
def change_node_attributes(id,is_root,local_id,title,ordid,namespace,description,short_description): 
    attributes = [{
        "typePublicId": IS_ROOT_NODE,
        "values": [is_root],
    },
     {   "typePublicId": TITLE,
         "values": [title]
    },
    {   "typePublicId": ORD_ID,
         "values": [ordid]

    },
         {   "typePublicId": SHORT_DESCRIPTION,
         "values": [""]
    },
         {   "typePublicId": DESCRIPTION,
         "values": [""]
    },
         {   "typePublicId": NAMESPACE,
         "values": [namespace]
    },
          {   "typePublicId": LOCAL_ID,
         "values": [local_id]
    },
                {   "typePublicId": DESCRIPTION,
         "values": [description]
    },
             {   "typePublicId": SHORT_DESCRIPTION,
         "values": [short_description]
    }
        ]
    for attribute in attributes:
        response = col.call(url_path=f'assets/{id}/attributes', json=attribute, method='put')

#Create Objects from csn file
def create_metadata_obj(obj, domain):
    obj_payload = {
        "name": obj,
        "typePublicId": DOMAIN_OBJECT,
        "domainId": domain
    }
    response = col.call(url_path=f'assets', json=obj_payload, method='post')
    return response['id']

#Create Nodes from csn file 
def create_metadata_nodes(node, node_fd):
    entity_payload = {
        "name": node,
        "typePublicId": NODES,
        "domainId": node_fd
    }
    response = col.call(url_path=f'assets', json=entity_payload, method='post')
    return response['id']

#Create mutiple relations between objects and nodes
def create_relation(obj_id, node_id):
    relation = [{
        "sourceId": obj_id,
        "targetId": node_id,
        "typePublicId": DOMAIN_OBJECT_TO_NODE
    }]
    response = col.call(url_path='relations/bulk', json=relation, method='post')


#Check if the name of the asset is case insensitive
def check_case_insenstive_match(incomming_name, existing_name):
    return incomming_name.lower() == existing_name.lower()

#Update the asset name if it is case insensitive
def update_asset_name(asset_id, updated_name):
    updated_json = {
        "name": updated_name,
        "displayName": updated_name}
    col.call(url_path=f'assets/{asset_id}', json=updated_json, method='patch')

#Get the nodes from collibra. Incase of just case insensitive match, update the name get the existing node uuid
def get_nodes(asset_name):
    response = col.call(url_path='assets', parameters={'name': asset_name, "domainId": METADATA_NODES, "nameMatchMode": "END"})
    if response and response.get('results'):
        if response.get('total') > 1: #Multiple matches found
            for result in response['results']:
                node_name_in_collibra = result.get('name')
                node_uuid_in_collibra = result.get('id')
                case_insens_match = check_case_insenstive_match(asset_name, node_name_in_collibra)
                if case_insens_match == True:
                    update_asset_name(node_uuid_in_collibra, asset_name) 
                    return node_name_in_collibra,node_uuid_in_collibra
        else:  #Single match found
            node_name_in_collibra = response['results'][0].get('name')
            node_uuid_in_collibra = response['results'][0].get('id')
            case_insens_match = check_case_insenstive_match(asset_name, node_name_in_collibra)
            if case_insens_match:
                update_asset_name(node_uuid_in_collibra, asset_name) 
                return node_name_in_collibra,node_uuid_in_collibra
    return None,None


#Get the objects from collibra. Incase of just case insensitive match, update the name get the existing object uuid
def get_objects(asset_name):
    response = col.call(url_path='assets', parameters={'name': asset_name, "domainId": METADATA_OBJECTS, "nameMatchMode": "END"})
    if response and response.get('results'):
        if response.get('total') > 1: #Multiple matches found
            for result in response['results']:
                dobj_name_in_collibra = result.get('name')
                dobj_uuid_in_collibra = result.get('id')
                case_insens_match = check_case_insenstive_match(asset_name, dobj_name_in_collibra)
                if case_insens_match == True:
                    update_asset_name(dobj_uuid_in_collibra, asset_name) 
                    return dobj_name_in_collibra, dobj_uuid_in_collibra
        else:  #Single match found
                dobj_name_in_collibra = response['results'][0].get('name')
                dobj_uuid_in_collibra = response['results'][0].get('id')
                case_insens_match = check_case_insenstive_match(asset_name, dobj_name_in_collibra)
                if case_insens_match:
                    update_asset_name(dobj_uuid_in_collibra, asset_name) 
                    return dobj_name_in_collibra, dobj_uuid_in_collibra
    return None,None

def get_odm_entities(asset_name):
    response = col.call(url_path='assets', parameters={'name': asset_name, "domainId": ODM_ENTITY_DOMAIN_ID, "nameMatchMode": "EXACT"})
    if response and response.get('results'):
        return response['results'][0]
    return None

def odm_to_domain_object_relation(odm_id, domain_id):
    relation = [{
        "sourceId": odm_id,
        "targetId": domain_id,
        "typePublicId": ODM_ENTITY_TO_DOMAIN_OBJECT
    }]
    response = col.call(url_path='relations/bulk', json=relation, method='post')


def delete_all_relation_domain_obj(domain_obj_id):
    relation_pub_id = DOMAIN_OBJECT_TO_NODE
    relations_resp = col.call(url_path=f'relations/', parameters={'sourceId': domain_obj_id, 'typePublicId': relation_pub_id})
    # get list of relation ids
    relation_ids = [relation['id'] for relation in relations_resp['results']]
    # send bulk delete
    col.call(url_path='relations/bulk', json=relation_ids, method='delete')
    log(f"Deleted all relations for domain object {domain_obj_id}", COLORS.RED)

def csn_import(csn_file):
    payload,odm_entity_name,title,local_id,ordid,namespace,description,short_description,major_version,minor_version,patch_version = extract_entities(csn_file)
    obj = list(payload.keys())[0]
    domain = METADATA_OBJECTS
    existing_obj,existing_obj_id = get_objects(obj) # Check if the object already exists and overwrite it
    log(f"Exact Matcch found for Domain Object: {existing_obj} with id: {existing_obj_id}", COLORS.GREEN)
    if existing_obj and obj.lower() == existing_obj.lower():
        obj_id = existing_obj_id
        delete_all_relation_domain_obj(obj_id) # delete all the relations of the domain object
    else:
        obj_id = create_metadata_obj(obj, domain)
        log(f"Domain Object created: {obj} with id: {obj_id}", COLORS.GREEN)
    change_domain_object_attributes(obj_id,local_id,title,ordid,namespace,description,short_description,major_version, minor_version, patch_version)
    nodes_fd = METADATA_NODES
    for node in payload[obj]:
        node_name = node['name']
        is_root = node['is_root']
        local_id = node['local_id']
        title = node['title']
        ordid = node['ordid']
        namespace = node['namespace']
        node_exists,nodes_exist_id = get_nodes(node_name) # Check if the node already exists and overwrite and relate it
        if node_exists and node_name.lower() == node_exists.lower():
            node_id = nodes_exist_id
            log(f"Exact Match found for Node: {node_exists} with id: {node_id}", COLORS.GREEN)
            create_relation(obj_id, node_id)
        else:
            node_id = create_metadata_nodes(node_name, nodes_fd)
            log(f"Node created: {node_name} with id: {node_id}", COLORS.GREEN)
            create_relation(obj_id, node_id)
        change_node_attributes(node_id, is_root,local_id,title,ordid,namespace,description,short_description)
    if odm_entity_name != None:
        odm_entity = get_odm_entities(odm_entity_name)
        if odm_entity:
            odm_id = odm_entity.get('id')
            odm_to_domain_object_relation(odm_id, obj_id)
        else:
            log(f"ODM entity '{odm_entity_name}' not found.", COLORS.YELLOW)

def domain_and_nodes_import(folder_path):
    for root, _, files in os.walk(folder_path):
        for filename in files:
            if filename.endswith('.json') and 'archive' not in root:
                csn_file = os.path.join(root, filename)
                csn_import(csn_file)
                archive_folder = os.path.join('domain_import/csn', os.path.relpath(root, folder_path), 'archive', filename)
                os.makedirs(os.path.dirname(archive_folder), exist_ok=True)
                shutil.move(csn_file, archive_folder)

domain_and_nodes_import('domain_import/csn')