import os
import subprocess
import shutil

# Define the paths
events_folder = os.path.join(os.path.dirname(__file__), '../../domain_import/events')
jar_file = os.path.join(os.path.dirname(__file__), '../../hcm_csn_converter/dfi-csn-converter-1.0.jar')
output_folder = os.path.join(os.path.dirname(__file__), '../../domain_import/csn')
wave2_jar_file = os.path.join(os.path.dirname(__file__), '../../hcm_csn_converter/dfi-csn-converter-wave2-1.0.jar')

# Recursive file search
json_files = []
for root, dirs, files in os.walk(events_folder):
    for file in files:
        if file.endswith('.json')  and 'archive' not in root:
            json_files.append(os.path.relpath(os.path.join(root, file), events_folder))

# Loop through each JSON file and run the JAR file
for json_file in json_files:
    json_file_path = os.path.join(events_folder, json_file)
    # Extract the output csn file name from the input events filename (e.g. 'assignment' from 'assignment_metadata.json' op : assignment_csn.json)
    file_name = os.path.basename(json_file).split('_')[0] # get the filename and split it based on '_' abd get the first element
    module_name = json_file.split('/')[0] # get the first element of the path to use as module name
    print(module_name)
    
    # Run the JAR file with the JSON file and the datasource name as arguments
    if module_name in ['career','performance']:
        command = ['java', '-jar', wave2_jar_file, json_file_path]
    else:
        command = ['java', '-jar', jar_file, json_file_path]
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print(f"Successfully processed {json_file}: {result.stdout}")
        if result.stderr:
            print(f"Warning: {result.stderr}")
        
        # Extract the actual output file path from the success message
        output_message = result.stdout.strip()
        output_file_path = output_message.split('Output written to: ')[-1]
        
        # Define the new output file path
        relative_path = os.path.dirname(json_file) #Get the folder of the json file to make the same directory in archive directory when moving it.
        # Extract the actual output file name from the output message generated by the JAR file
        actual_output_file_name = os.path.basename(output_file_path)
        
        # Define the new output file path using the actual output file name
        new_output_file_path = os.path.join(output_folder, relative_path, actual_output_file_name)
        
        # Ensure the output subdirectory exists
        os.makedirs(os.path.dirname(new_output_file_path), exist_ok=True) # if exists ignore or create to place the files
        
        # Move or rename the actual output file to the new location
        shutil.move(output_file_path, new_output_file_path) #now move from the original output pushed by jar file to output path.
        
        # Define the archive subfolder path within the same events subfolder
        archive_subfolder = os.path.join(events_folder, relative_path, 'archive') #archive the input events after moving to output to csn folder
        os.makedirs(archive_subfolder, exist_ok=True)
        
        # Move the original JSON file to the archive location
        shutil.move(json_file_path, os.path.join(archive_subfolder, os.path.basename(json_file_path)))
        
        print(f"Output file moved to: {new_output_file_path}")
        print(f"Archived file moved to: {os.path.join(archive_subfolder, os.path.basename(json_file_path))}")
    except subprocess.CalledProcessError as e:
        print(f"Error processing {json_file}: {e.stderr}")
    except Exception as e:
        print(f"Unexpected error: {e}")
