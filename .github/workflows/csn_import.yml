name: "[<PERSON>O<PERSON>] CSN Import --> Collibra Domain Objects and Nodes"

on:
  push:
    branches:
      - main
    paths:
      - "domain_import/csn/**/*.json"
  workflow_dispatch:
    inputs:
      folder:
        description: "Path to the folder containing CSN files"
        required: false
        default: "./domain_import/csn"

env:
  ### LOB Configuration
  LOB_CONST: HCM
  CSN_IMPORT_FOLDER: ${{ github.event.inputs.folder || './domain_import/csn' }}
### LOB Configuration end

jobs:
  csn_import:
    if: github.actor != 'bdc-dp-auto-serviceuser'
    runs-on: [solinas-small]
    permissions: write-all
    concurrency:
      group: csn-import-group-dev

    steps:
      - name: Run CSN Import Action
        uses: bdc/CollibraRepoDP/.github/actions-dev/csn_import@main
