name: PR Diff Comment
permissions: write-all
on:
  pull_request:
    paths:
      - 'domain_import/csn/**/*.json'
jobs:
  diff-comment:
    runs-on: [self-hosted, solinas]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Calculate diffs and output to individual files
        id: diff
        run: |
          mkdir -p diffs
          for file in $(git diff --name-only "${{ github.event.pull_request.base.sha }}" "${{ github.event.pull_request.head.sha }}" | grep '^domain_import/csn/.*\.json$'); do
            archive_dir="$(dirname "$file")/archive"
            csn_name="$(basename "$file" .json)"
            archive_files=$(ls "$archive_dir"/"${csn_name}"*.json 2>/dev/null || true)
            if [ -n "$archive_files" ]; then
              if [ -f "$archive_dir/$(basename "$file")" ]; then
                archive_file="$archive_dir/$(basename "$file")"
              else
                archive_file=$(ls "$archive_dir"/"${csn_name}"*.json | sort -V | tail -n 1)
              fi
              diff_output=$(diff -u "$archive_file" "$file" || true)
              if [ -n "$diff_output" ]; then
                normalized=$(echo "$file" | tr '/' '_')
                diff_file="diffs/${normalized}.diff"
                echo '```diff' >> "$diff_file"
                echo "$diff_output" >> "$diff_file"
                echo '```' >> "$diff_file"
              fi
            fi
          done
      - name: Post a PR comment for each diff file using gh cli
        env:
          GH_TOKEN: ${{ secrets.BDC_DP_AUTO }}
        run: |
          echo "${{ secrets.GITHUB_TOKEN }}" | gh auth login -h "github.tools.sap" --with-token
          for diff in diffs/*.diff; do
            [ -e "$diff" ] || continue
            gh pr comment ${{ github.event.pull_request.number }} --body-file "$diff"
          done
