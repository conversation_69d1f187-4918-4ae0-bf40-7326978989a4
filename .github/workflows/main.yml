name: List PRs by bdc in metadata repo

on:
  # schedule:
  #   - cron: '0 */12 * * *' # Every 12 hours
  workflow_dispatch:

jobs:
  list-prs:
    runs-on: [self-hosted, solinas]
    steps:

      - name: Authenticate GitHub CLI
        run: echo "${{ secrets.ADOT }}" | gh auth login -h "github.tools.sap" --with-token

      - name: List open PRs by user "bdc"
        run: |
          echo "Open PRs by bdc in bdc/metadata:"
          gh pr list \
            --repo bdc-fos/dp-metadata \
            --state open \
            --author bdc-dp-auto-serviceuser \
