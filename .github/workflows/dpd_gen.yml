name: "[<PERSON><PERSON><PERSON>] Intermedate DPD Generation"

on:
  workflow_dispatch:
  # Runs daily at 4am IST | 22:30 UTC
  schedule:
    - cron: '0 22 * * *'

env:
  ##### LOB Specific
  LOB_CONST: HCM
  OUTPUT_DIR_INT_DPD: "int_dpd/DPDfiles/"
##### LOB Specific End

jobs:
  dpd_gen:
    runs-on: [solinas-small]
    permissions: write-all
    concurrency:
      group: dpd-gen-group-dev

    steps:
      - name: Run DPD Gen Action
        uses: bdc/CollibraRepoDP/.github/actions-dev/dpd_gen@main
