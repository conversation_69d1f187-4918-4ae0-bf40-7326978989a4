name: Validate Data Product Versions
on:
  workflow_dispatch:
  # Runs daily
#   schedule:
#     - cron: '7 2 * * *'
jobs:
  Validate-DPVs:
    runs-on: [self-hosted, solinas]
    steps:
      - name: Check out CollibraRepoDP repository
        uses: actions/checkout@v4
        with:
          repository: bdc/CollibraRepoDP
          token: ${{ secrets.GITHUB_TOKEN }}
          ref: DPE-5282/CollibraValidation

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - name: Install Deps
        run: |
          pip install .
      - name: Validate Data Product Versions
        run: |
          python3 cron/collibraValidation/collibraValidation.py
        env:
          COLLIBRA_URL: https://sap-bdc-dev.collibra.com
          COLLIBRA_USERNAME: BDCDPRepo
          COLLIBRA_PASSWORD: ${{ secrets.COL_PWD }}