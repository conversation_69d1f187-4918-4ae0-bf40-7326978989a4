name: "ZArchive Set Valid for Namespace"

on: workflow_dispatch

jobs:
  set-valid-for-namespace:
    runs-on: [self-hosted, solinas]
    steps:
      - name: Check out CollibraRepoDP repository
        uses: actions/checkout@v4
        with:
            repository: bdc/CollibraRepoDP
            token: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install dependencies
        shell: bash
        run: |
          pip install .
          cd ..

      - name: Update BDC Namespace
        shell: bash
        run: python scripts/setValidForNamespaces/setValidForNamespaces.py
        env:
          COLLIBRA_URL: https://sap2-dev.collibra.com
          COLLIBRA_USERNAME: BDCDPRepo
          COLLIBRA_PASSWORD: ${{ secrets.COL_PWD }}