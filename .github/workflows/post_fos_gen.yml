name: "[Automated] Post FOS Stubs Generation - Validation" # and auto merge to main
on:
  pull_request:
    types:
      - synchronize
    branches:
      - main
    paths:
      - "fos_dpd/**"
jobs:
  fos_validation:
    runs-on: [solinas-small]
    permissions: write-all
    steps:
      - name: Run FOS Schema Validator
        uses: bdc/CollibraRepoDP/.github/actions-dev/fos_schema_validator@main
        env:
          GH_PR_NUM: ${{ github.event.pull_request.number }}

  # csn_cpa_api_validator:
  #   name: "CPA API Validator - Validate CSN files"
  #   runs-on: [solinas-small]
  #   steps:
  #     - name: Check out repository
  #       uses: actions/checkout@v4
  #       with:
  #         token: ${{ secrets.BDC_DP_AUTO }}
  #         ref: ${{ github.head_ref }}

  #     - name: Run CPA API Validator
  #       uses: CPA/api-metadata-validator@main
  #       with:
  #         files: ./fos_dpd/csn_documents/**/*.json
  #         format: json
  #         output: ./results/csn-cpa-validation-result.json
  #         verbose: true
  #         github-token: ${{ secrets.GITHUB_TOKEN }}
  #         pull-request-review: true
  #         result-page-name: CSN Validation
