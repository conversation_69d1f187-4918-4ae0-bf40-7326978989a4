name: FOS PR Creation

on:
  workflow_dispatch:
    inputs:
      user_input:
        description: "Enter the Package Name"
        required: false

jobs:
  fos_pr:
    runs-on: [solinas-small]

    steps:
      # Step 1: Check out the target repository
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.BDC_DP_AUTO }}

      - name: Run Fos PR Automation
        uses: bdc/CollibraRepoDP/.github/actions-dev/fos_pr@fos_pr_rework
        with:
          const: HCM
          fos_folder_name:
        env:
          COLLIBRA_PASSWORD: ${{ secrets.COL_PWD }}
          COLLIBRA_USERNAME: BDCDPRepo
          COLLIBRA_URL: https://sap2-dev.collibra.com
          BDC_DP_AUTO: ${{ secrets.BDC_DP_AUTO }}
          GH_PAT: ${{ secrets.GITHUB_TOKEN }}
