name: "fos_dpd to fos_dpd_prod"

on:
  workflow_dispatch:


jobs:
  stage-data-products:
    runs-on: [self-hosted, solinas]
    steps:
    # Checkout the repository code
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
        
    # Setup Python
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - run: pip install -r src/CollibraScripts/requirements.txt

      # Run dp-release script to generate data products
      - name: Run dp-release script
        env:
          COLLIBRA_URL: https://sap2-dev.collibra.com/
          COLLIBRA_USERNAME: BDCDPRepo
          COLLIBRA_PASSWORD: BDCindiadprepo777@
        run: |
          python src/CollibraScripts/dp-release.py run
          cd src
          ls -l

      # Move files to stage
      - name: Move files to stage
        env:
          COLLIBRA_URL: https://sap2-dev.collibra.com/
          COLLIBRA_USERNAME: BDCDPRepo
          COLLIBRA_PASSWORD: BDCindiadprepo777@
        run: |
          python src/CollibraScripts/dp-release.py move
          rm -rf src/CollibraScripts/resolved_paths.json
          rm -rf src/CollibraScripts/__pycache__

      # Create a unique branch name and push changes
      - name: Create unique variable and store in GITHUB_ENV
        shell: bash
        run: |
            echo "branch_name=STAGE_DP_$(date +'%Y%m%d_%H%M%S')" >> $GITHUB_OUTPUT
            BRANCH_NAME="STAGE_DP_$(date +'%Y%m%d_%H%M%S')"
            echo "Branch name is: $BRANCH_NAME"
            echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV

      # Create a new branch and commit changes
      - name: Make new Branch
        run: |
          git checkout -b ${{env.BRANCH_NAME}}

      # Configure git user
      - name: Configure git
        run: |
          git config --global user.name "I761732"
          git config --global user.email "<EMAIL>"

      # Commit and push changes to the new branch
      - name: Commit and Push Changes
        if: success()
        run: |
          git add .
          if git diff --quiet --cached; then
            echo "No changes to commit."
            exit 0
          fi
          git commit -m "[Actions] Stage Data Products"
          git push origin ${{ env.BRANCH_NAME }}
          echo "${{ secrets.GH_AD }}" | gh auth login -h "github.tools.sap" --with-token
          gh pr create \
              --body "Please check the changes done." \
              --title "STAGE_DP" \
              --head "${{ env.BRANCH_NAME }}" \
              --base "main" \
              --reviewer "I761732"