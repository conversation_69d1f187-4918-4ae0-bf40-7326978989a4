name: "Package Gen Workflow"

on:
  push:
    branches:
      - main
    paths:
      - "fos_dpd/**"
  workflow_dispatch:
    inputs:
      folder:
        description: "Path to save the generated files"
        required: true
        default: "./fos_dpd/packages/"

env:
  ##### LOB Specific
  LOB_CONST: HCM
  PACKAGE_GEN_FOLDER: ${{ github.event.inputs.folder || './fos_dpd/packages/' }}
##### LOB Specific End

jobs:
  pkg_export:
    runs-on: [solinas-small]
    permissions: write-all
    steps:
      - name: Package Gen
        uses: bdc/CollibraRepoDP/.github/actions-dev/package_gen@main
