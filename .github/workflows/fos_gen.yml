name: "[Automated] Intermediate DPD Validation and FOS Stubs Generate"

on:
  pull_request:
    types: [opened]
    branches:
      - main
    paths:
      - "int_dpd/DPDfiles/**"

env:
  ##### LOB Specific
  INPUT_DIR_INT_DPD: "int_dpd/DPDfiles"
##### LOB Specific End

jobs:
  int_val:
    runs-on: [solinas-small]
    permissions: write-all

    steps:
      - name: Run DPD Schema Validation
        id: schema_validation
        continue-on-error: true
        uses: bdc/CollibraRepoDP/.github/actions-dev/int_schema_validator@main
        env:
          GH_PR_NUM: ${{ github.event.pull_request.number }}

      - name: Run int dpd validation
        id: int_validation
        continue-on-error: true
        uses: bdc/CollibraRepoDP/.github/actions-dev/int_ord_validation@main

      - name: Fail if any validation failed
        if: steps.schema_validation.outcome == 'failure' || steps.int_validation.outcome == 'failure'
        run: |
          echo "One or more validations failed."
          exit 1

  fos_gen:
    runs-on: [solinas-small]
    permissions: write-all
    needs: int_val
    steps:
      - name: Run FOS Gen Action
        uses: bdc/CollibraRepoDP/.github/actions-dev/fos_stubs_gen@main
        with:
          input_dir: ${{ env.INPUT_DIR_INT_DPD }}
